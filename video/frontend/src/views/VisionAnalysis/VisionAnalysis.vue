<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 页面头部 -->
    <div class="bg-white shadow">
      <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center">
          <h1 class="text-3xl font-bold text-gray-900">视觉分析</h1>
          <button @click="refreshModels"
            class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium">
            刷新模型
          </button>
        </div>
      </div>
    </div>

    <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
      <!-- 模型状态 -->
      <div class="bg-white rounded-lg shadow mb-6">
        <div class="p-6">
          <h2 class="text-xl font-semibold text-gray-900 mb-4">可用模型</h2>
          <div v-if="modelsLoading" class="text-center py-4">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
            <p class="mt-2 text-sm text-gray-500">加载模型信息...</p>
          </div>
          <div v-else-if="availableModels.length === 0" class="text-center py-4">
            <p class="text-sm text-gray-500">暂无可用模型</p>
          </div>
          <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div v-for="model in availableModels" :key="model" 
              class="border rounded-lg p-4 hover:border-primary-300 transition-colors"
              :class="{ 'border-primary-500 bg-primary-50': selectedModel === model }">
              <div class="flex items-center justify-between">
                <div>
                  <h3 class="font-medium text-gray-900">{{ model }}</h3>
                  <p class="text-sm text-gray-500">{{ getModelStatus(model) }}</p>
                </div>
                <button @click="selectedModel = model"
                  class="text-sm px-3 py-1 rounded-md"
                  :class="selectedModel === model ? 'bg-primary-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'">
                  {{ selectedModel === model ? '已选择' : '选择' }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 分析功能 -->
      <div class="bg-white rounded-lg shadow">
        <div class="p-6">
          <h2 class="text-xl font-semibold text-gray-900 mb-4">图片分析</h2>
          
          <!-- 上传区域 -->
          <div class="mb-6">
            <label class="block cursor-pointer">
              <input type="file" class="sr-only" multiple accept="image/*" @change="handleFileUpload" />
              <div class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors">
                <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                  <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                </svg>
                <div class="mt-4">
                  <span class="mt-2 block text-sm font-medium text-gray-900">
                    点击上传或拖拽图片到此处
                  </span>
                  <p class="mt-1 text-xs text-gray-500">
                    支持 JPG, PNG, GIF 等格式，可选择多张图片
                  </p>
                </div>
              </div>
            </label>
          </div>

          <!-- 已上传的图片 -->
          <div v-if="uploadedImages.length > 0" class="mb-6">
            <h3 class="text-sm font-medium text-gray-900 mb-3">已上传图片 ({{ uploadedImages.length }})</h3>
            <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
              <div v-for="(image, index) in uploadedImages" :key="index" class="relative">
                <img :src="image.preview" :alt="image.name" 
                  class="w-full h-24 object-cover rounded-lg border">
                <button @click="removeImage(index)"
                  class="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600">
                  ×
                </button>
                <p class="mt-1 text-xs text-gray-500 truncate">{{ image.name }}</p>
              </div>
            </div>
          </div>

          <!-- 分析问题 -->
          <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">分析问题</label>
            <textarea v-model="analysisQuestion" rows="3"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              placeholder="请输入您想要分析的问题，例如：请详细描述这些图片的内容"></textarea>
          </div>

          <!-- 分析按钮 -->
          <div class="flex space-x-4">
            <button @click="analyzeImages" :disabled="!canAnalyze || analyzing"
              class="bg-primary-600 hover:bg-primary-700 disabled:bg-gray-400 text-white px-6 py-2 rounded-md text-sm font-medium flex items-center space-x-2">
              <svg v-if="analyzing" class="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              <span>{{ analyzing ? '分析中...' : '开始分析' }}</span>
            </button>
            <button @click="clearAll" :disabled="analyzing"
              class="bg-gray-600 hover:bg-gray-700 disabled:bg-gray-400 text-white px-6 py-2 rounded-md text-sm font-medium">
              清空
            </button>
          </div>

          <!-- 分析结果 -->
          <div v-if="analysisResult" class="mt-8">
            <h3 class="text-lg font-medium text-gray-900 mb-4">分析结果</h3>
            <div class="bg-gray-50 rounded-lg p-6">
              <div class="mb-4">
                <span class="text-sm text-gray-500">使用模型：</span>
                <span class="text-sm font-medium text-gray-900">{{ analysisResult.model_used }}</span>
                <span class="ml-4 text-sm text-gray-500">处理时间：</span>
                <span class="text-sm font-medium text-gray-900">{{ formatProcessingTime(analysisResult.result?.processing_time) }}</span>
              </div>
              <div class="prose max-w-none">
                <p class="text-gray-800 whitespace-pre-wrap">{{ analysisResult.result?.content }}</p>
              </div>
            </div>
          </div>

          <!-- 错误信息 -->
          <div v-if="errorMessage" class="mt-6">
            <div class="bg-red-50 border border-red-200 rounded-lg p-4">
              <div class="flex">
                <svg class="h-5 w-5 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                    d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div class="ml-3">
                  <h3 class="text-sm font-medium text-red-800">分析失败</h3>
                  <p class="mt-1 text-sm text-red-700">{{ errorMessage }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useAppStore } from '@/stores/app'
import client from '@/api/client'

const appStore = useAppStore()

// 响应式数据
const modelsLoading = ref(false)
const availableModels = ref([])
const modelsInfo = ref({})
const selectedModel = ref('')
const uploadedImages = ref([])
const analysisQuestion = ref('请详细描述这些图片的内容和关联性')
const analyzing = ref(false)
const analysisResult = ref(null)
const errorMessage = ref('')

// 计算属性
const canAnalyze = computed(() => {
  return uploadedImages.value.length > 0 && analysisQuestion.value.trim() && selectedModel.value
})

// 方法
const loadModels = async () => {
  modelsLoading.value = true
  try {
    const response = await client.get('/vision/models')
    availableModels.value = response.data.available_models || []
    modelsInfo.value = response.data.models_info || {}
    
    // 自动选择第一个可用模型
    if (availableModels.value.length > 0 && !selectedModel.value) {
      selectedModel.value = availableModels.value[0]
    }
  } catch (error) {
    appStore.showError('加载失败', '无法加载视觉模型列表')
  } finally {
    modelsLoading.value = false
  }
}

const refreshModels = async () => {
  await loadModels()
  appStore.showSuccess('刷新成功', '模型列表已更新')
}

const getModelStatus = (modelName) => {
  const info = modelsInfo.value[modelName]
  if (info?.initialized) {
    return '可用'
  }
  return '不可用'
}

const handleFileUpload = (event) => {
  const files = Array.from(event.target.files)
  files.forEach(file => {
    if (file.type.startsWith('image/')) {
      const reader = new FileReader()
      reader.onload = (e) => {
        uploadedImages.value.push({
          file: file,
          name: file.name,
          preview: e.target.result
        })
      }
      reader.readAsDataURL(file)
    }
  })
  
  // 清空input
  event.target.value = ''
}

const removeImage = (index) => {
  uploadedImages.value.splice(index, 1)
}

const analyzeImages = async () => {
  if (!canAnalyze.value) return
  
  analyzing.value = true
  analysisResult.value = null
  errorMessage.value = ''
  
  try {
    const formData = new FormData()
    
    // 添加图片文件
    uploadedImages.value.forEach(image => {
      formData.append('files', image.file)
    })
    
    // 添加其他参数
    formData.append('question', analysisQuestion.value)
    formData.append('model_name', selectedModel.value)
    
    const response = await client.post('/vision/upload-and-analyze', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    
    analysisResult.value = response.data
    appStore.showSuccess('分析完成', '图片分析已完成')
    
  } catch (error) {
    errorMessage.value = error.response?.data?.detail || '分析失败，请稍后重试'
    appStore.showError('分析失败', errorMessage.value)
  } finally {
    analyzing.value = false
  }
}

const clearAll = () => {
  uploadedImages.value = []
  analysisResult.value = null
  errorMessage.value = ''
  analysisQuestion.value = '请详细描述这些图片的内容和关联性'
}

const formatProcessingTime = (time) => {
  if (!time) return '未知'
  return `${time.toFixed(2)}秒`
}

// 生命周期
onMounted(() => {
  loadModels()
})
</script>

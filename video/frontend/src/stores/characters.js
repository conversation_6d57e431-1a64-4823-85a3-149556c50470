import { defineStore } from 'pinia'
import { charactersAPI } from '@/api/characters'

export const useCharactersStore = defineStore('characters', {
  state: () => ({
    // 人物角色列表
    characters: [],
    // 当前选中的人物角色
    currentCharacter: null,
    // 人物出现记录
    appearances: [],
    // 搜索结果
    searchResults: [],
    // 加载状态
    loading: false,
    // 分页信息
    pagination: {
      total: 0,
      skip: 0,
      limit: 20
    },
    // 过滤条件
    filters: {
      taskId: null,
      videoId: null,
      searchQuery: ''
    }
  }),

  getters: {
    /**
     * 获取指定任务的人物角色
     */
    getCharactersByTask: (state) => (taskId) => {
      return state.characters.filter(character => character.task_id === taskId)
    },

    /**
     * 获取主要人物角色（按出现时长排序）
     */
    getMainCharacters: (state) => (taskId, limit = 5) => {
      return state.characters
        .filter(character => character.task_id === taskId)
        .sort((a, b) => b.total_duration - a.total_duration)
        .slice(0, limit)
    },

    /**
     * 获取人物角色统计信息
     */
    getCharacterStats: (state) => (taskId) => {
      const taskCharacters = state.characters.filter(character => character.task_id === taskId)
      
      return {
        totalCharacters: taskCharacters.length,
        totalDuration: taskCharacters.reduce((sum, char) => sum + char.total_duration, 0),
        avgConfidence: taskCharacters.length > 0 
          ? taskCharacters.reduce((sum, char) => sum + char.confidence, 0) / taskCharacters.length 
          : 0,
        namedCharacters: taskCharacters.filter(char => char.name && char.name !== char.name.match(/^角色_\d+$/)).length
      }
    },

    /**
     * 检查是否有人物角色数据
     */
    hasCharacters: (state) => (taskId) => {
      return state.characters.some(character => character.task_id === taskId)
    }
  },

  actions: {
    /**
     * 获取任务的人物角色列表
     */
    async fetchTaskCharacters(taskId, params = {}) {
      this.loading = true
      try {
        const response = await charactersAPI.getTaskCharacters(taskId, {
          skip: this.pagination.skip,
          limit: this.pagination.limit,
          ...params
        })

        this.characters = response.characters
        this.pagination.total = response.total
        this.pagination.skip = response.skip
        this.pagination.limit = response.limit
        this.filters.taskId = taskId

        return response
      } catch (error) {
        console.error('获取人物角色列表失败:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    /**
     * 获取人物角色详情
     */
    async fetchCharacter(characterId) {
      this.loading = true
      try {
        const character = await charactersAPI.getCharacter(characterId)
        this.currentCharacter = character
        
        // 更新列表中的对应项
        const index = this.characters.findIndex(c => c.id === characterId)
        if (index !== -1) {
          this.characters[index] = character
        }

        return character
      } catch (error) {
        console.error('获取人物角色详情失败:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    /**
     * 更新人物角色信息
     */
    async updateCharacter(characterId, data) {
      this.loading = true
      try {
        const updatedCharacter = await charactersAPI.updateCharacter(characterId, data)
        
        // 更新当前人物角色
        if (this.currentCharacter && this.currentCharacter.id === characterId) {
          this.currentCharacter = updatedCharacter
        }
        
        // 更新列表中的对应项
        const index = this.characters.findIndex(c => c.id === characterId)
        if (index !== -1) {
          this.characters[index] = updatedCharacter
        }

        return updatedCharacter
      } catch (error) {
        console.error('更新人物角色失败:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    /**
     * 获取人物出现记录
     */
    async fetchCharacterAppearances(characterId, params = {}) {
      this.loading = true
      try {
        const response = await charactersAPI.getCharacterAppearances(characterId, params)
        this.appearances = response.appearances
        return response
      } catch (error) {
        console.error('获取人物出现记录失败:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    /**
     * 搜索相似人物角色
     */
    async searchSimilarCharacters(searchData) {
      this.loading = true
      try {
        const response = await charactersAPI.searchSimilarCharacters(searchData)
        this.searchResults = response.results
        return response
      } catch (error) {
        console.error('搜索相似人物角色失败:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    /**
     * 删除人物角色
     */
    async deleteCharacter(characterId) {
      this.loading = true
      try {
        await charactersAPI.deleteCharacter(characterId)
        
        // 从列表中移除
        this.characters = this.characters.filter(c => c.id !== characterId)
        
        // 清除当前选中的人物角色
        if (this.currentCharacter && this.currentCharacter.id === characterId) {
          this.currentCharacter = null
        }

        return true
      } catch (error) {
        console.error('删除人物角色失败:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    /**
     * 设置当前人物角色
     */
    setCurrentCharacter(character) {
      this.currentCharacter = character
    },

    /**
     * 清除当前人物角色
     */
    clearCurrentCharacter() {
      this.currentCharacter = null
    },

    /**
     * 清除搜索结果
     */
    clearSearchResults() {
      this.searchResults = []
    },

    /**
     * 设置过滤条件
     */
    setFilters(filters) {
      this.filters = { ...this.filters, ...filters }
    },

    /**
     * 重置状态
     */
    reset() {
      this.characters = []
      this.currentCharacter = null
      this.appearances = []
      this.searchResults = []
      this.loading = false
      this.pagination = {
        total: 0,
        skip: 0,
        limit: 20
      }
      this.filters = {
        taskId: null,
        videoId: null,
        searchQuery: ''
      }
    },

    /**
     * 获取人物角色头像URL
     */
    getAvatarUrl(characterId) {
      return charactersAPI.getCharacterAvatarUrl(characterId)
    }
  }
})

import client from './client'

/**
 * 人物角色相关API
 */
export const charactersAPI = {
  /**
   * 获取任务的人物角色列表
   * @param {number} taskId - 任务ID
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  getTaskCharacters(taskId, params = {}) {
    return client.get(`/characters/tasks/${taskId}/characters`, { params })
  },

  /**
   * 获取人物角色详情
   * @param {number} characterId - 人物ID
   * @returns {Promise}
   */
  getCharacter(characterId) {
    return client.get(`/characters/${characterId}`)
  },

  /**
   * 更新人物角色信息
   * @param {number} characterId - 人物ID
   * @param {Object} data - 更新数据
   * @returns {Promise}
   */
  updateCharacter(characterId, data) {
    return client.put(`/characters/${characterId}`, data)
  },

  /**
   * 获取人物角色的出现记录
   * @param {number} characterId - 人物ID
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  getCharacterAppearances(characterId, params = {}) {
    return client.get(`/characters/${characterId}/appearances`, { params })
  },

  /**
   * 获取人物角色头像
   * @param {number} characterId - 人物ID
   * @returns {string} 头像URL
   */
  getCharacterAvatarUrl(characterId) {
    return `/api/v1/characters/${characterId}/avatar`
  },

  /**
   * 通过人脸图片搜索相似的人物角色
   * @param {Object} searchData - 搜索数据
   * @returns {Promise}
   */
  searchSimilarCharacters(searchData) {
    return client.post('/characters/search', searchData)
  },

  /**
   * 删除人物角色
   * @param {number} characterId - 人物ID
   * @returns {Promise}
   */
  deleteCharacter(characterId) {
    return client.delete(`/characters/${characterId}`)
  },

  /**
   * 获取任务的人物角色统计信息
   * @param {number} taskId - 任务ID
   * @returns {Promise}
   */
  getTaskCharacterStats(taskId) {
    return client.get(`/characters/tasks/${taskId}/stats`)
  },

  /**
   * 合并人物角色
   * @param {Object} mergeData - 合并数据
   * @returns {Promise}
   */
  mergeCharacters(mergeData) {
    return client.post('/characters/merge', mergeData)
  },

  /**
   * 拆分人物角色
   * @param {Object} splitData - 拆分数据
   * @returns {Promise}
   */
  splitCharacter(splitData) {
    return client.post('/characters/split', splitData)
  }
}

export default charactersAPI

<template>
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
      <!-- 头部 -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">编辑角色信息</h3>
        <button
          @click="$emit('close')"
          class="text-gray-400 hover:text-gray-600 transition-colors"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <!-- 内容 -->
      <div class="p-6">
        <!-- 角色头像 -->
        <div class="flex justify-center mb-6">
          <div class="relative">
            <img
              v-if="character.avatar_url"
              :src="character.avatar_url"
              :alt="character.name || '未命名角色'"
              class="w-24 h-24 rounded-full object-cover border-4 border-gray-200"
            />
            <div v-else class="w-24 h-24 rounded-full bg-gray-200 flex items-center justify-center border-4 border-gray-200">
              <svg class="w-12 h-12 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
              </svg>
            </div>
          </div>
        </div>

        <!-- 表单 -->
        <form @submit.prevent="handleSubmit">
          <div class="space-y-4">
            <!-- 角色名称 -->
            <div>
              <label for="characterName" class="block text-sm font-medium text-gray-700 mb-2">
                角色名称
              </label>
              <input
                id="characterName"
                v-model="formData.name"
                type="text"
                placeholder="请输入角色名称"
                class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                :class="{ 'border-red-300': errors.name }"
              />
              <p v-if="errors.name" class="mt-1 text-sm text-red-600">{{ errors.name }}</p>
            </div>

            <!-- 角色信息展示 -->
            <div class="bg-gray-50 rounded-lg p-4 space-y-2">
              <div class="flex justify-between text-sm">
                <span class="text-gray-600">识别置信度:</span>
                <span class="font-medium">{{ Math.round(character.confidence * 100) }}%</span>
              </div>
              <div class="flex justify-between text-sm">
                <span class="text-gray-600">出现次数:</span>
                <span class="font-medium">{{ character.appearance_count }}</span>
              </div>
              <div class="flex justify-between text-sm">
                <span class="text-gray-600">总时长:</span>
                <span class="font-medium">{{ formatDuration(character.total_duration) }}</span>
              </div>
              <div class="flex justify-between text-sm">
                <span class="text-gray-600">视频数量:</span>
                <span class="font-medium">{{ character.video_count }}</span>
              </div>
            </div>
          </div>

          <!-- 按钮 -->
          <div class="flex space-x-3 mt-6">
            <button
              type="button"
              @click="$emit('close')"
              class="flex-1 bg-gray-300 text-gray-700 px-4 py-2 rounded-md font-medium hover:bg-gray-400 transition-colors"
            >
              取消
            </button>
            <button
              type="submit"
              :disabled="saving"
              class="flex-1 bg-blue-600 text-white px-4 py-2 rounded-md font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <span v-if="saving" class="flex items-center justify-center">
                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                保存中...
              </span>
              <span v-else>保存</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, watch } from 'vue'
import { useCharactersStore } from '@/stores/characters'

export default {
  name: 'CharacterEditModal',
  props: {
    character: {
      type: Object,
      required: true
    }
  },
  emits: ['close', 'updated'],
  setup(props, { emit }) {
    const charactersStore = useCharactersStore()
    
    // 响应式数据
    const saving = ref(false)
    const formData = reactive({
      name: ''
    })
    const errors = reactive({
      name: ''
    })

    // 初始化表单数据
    const initFormData = () => {
      formData.name = props.character.name || ''
    }

    // 验证表单
    const validateForm = () => {
      errors.name = ''
      
      if (!formData.name.trim()) {
        errors.name = '请输入角色名称'
        return false
      }
      
      if (formData.name.trim().length > 50) {
        errors.name = '角色名称不能超过50个字符'
        return false
      }
      
      return true
    }

    // 提交表单
    const handleSubmit = async () => {
      if (!validateForm()) return

      saving.value = true
      try {
        await charactersStore.updateCharacter(props.character.id, {
          name: formData.name.trim()
        })
        
        emit('updated')
      } catch (error) {
        console.error('更新角色失败:', error)
        // 这里可以显示错误提示
      } finally {
        saving.value = false
      }
    }

    // 格式化时长
    const formatDuration = (seconds) => {
      if (!seconds || seconds < 0) return '0秒'
      
      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      const secs = Math.floor(seconds % 60)
      
      if (hours > 0) {
        return `${hours}时${minutes}分${secs}秒`
      } else if (minutes > 0) {
        return `${minutes}分${secs}秒`
      } else {
        return `${secs}秒`
      }
    }

    // 监听角色变化
    watch(() => props.character, initFormData, { immediate: true })

    // 初始化
    initFormData()

    return {
      // 数据
      saving,
      formData,
      errors,
      
      // 方法
      handleSubmit,
      formatDuration
    }
  }
}
</script>

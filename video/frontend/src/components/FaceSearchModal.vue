<template>
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-hidden">
      <!-- 头部 -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">人脸相似度搜索</h3>
        <button
          @click="$emit('close')"
          class="text-gray-400 hover:text-gray-600 transition-colors"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <!-- 内容 -->
      <div class="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
        <!-- 上传区域 -->
        <div class="mb-6">
          <label class="block text-sm font-medium text-gray-700 mb-2">
            上传人脸图片
          </label>
          <div
            @drop="handleDrop"
            @dragover.prevent
            @dragenter.prevent
            class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors"
            :class="{ 'border-blue-400 bg-blue-50': isDragging }"
          >
            <input
              ref="fileInput"
              type="file"
              accept="image/*"
              @change="handleFileSelect"
              class="hidden"
            />
            
            <div v-if="!selectedImage">
              <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
              </svg>
              <div class="mt-4">
                <button
                  @click="$refs.fileInput.click()"
                  class="text-blue-600 hover:text-blue-500 font-medium"
                >
                  点击上传图片
                </button>
                <p class="text-gray-500">或拖拽图片到此处</p>
              </div>
              <p class="text-xs text-gray-400 mt-2">
                支持 JPG、PNG 格式，建议包含清晰的人脸
              </p>
            </div>

            <div v-else class="space-y-4">
              <img
                :src="selectedImage"
                alt="选中的图片"
                class="mx-auto max-h-48 rounded-lg"
              />
              <div class="flex space-x-2 justify-center">
                <button
                  @click="$refs.fileInput.click()"
                  class="px-4 py-2 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
                >
                  重新选择
                </button>
                <button
                  @click="clearImage"
                  class="px-4 py-2 text-sm bg-red-100 text-red-700 rounded-md hover:bg-red-200 transition-colors"
                >
                  清除图片
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 搜索参数 -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              相似度阈值
            </label>
            <input
              v-model.number="searchParams.similarity_threshold"
              type="range"
              min="0.3"
              max="1.0"
              step="0.05"
              class="w-full"
            />
            <div class="text-sm text-gray-500 mt-1">
              {{ Math.round(searchParams.similarity_threshold * 100) }}%
            </div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              返回结果数量
            </label>
            <select
              v-model.number="searchParams.top_k"
              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="5">5个</option>
              <option value="10">10个</option>
              <option value="20">20个</option>
              <option value="50">50个</option>
            </select>
          </div>
        </div>

        <!-- 搜索按钮 -->
        <div class="mb-6">
          <button
            @click="performSearch"
            :disabled="!selectedImage || searching"
            class="w-full bg-blue-600 text-white px-4 py-2 rounded-md font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <span v-if="searching" class="flex items-center justify-center">
              <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              搜索中...
            </span>
            <span v-else>开始搜索</span>
          </button>
        </div>

        <!-- 搜索结果 -->
        <div v-if="searchResults.length > 0" class="space-y-4">
          <h4 class="text-lg font-medium text-gray-900">搜索结果</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div
              v-for="result in searchResults"
              :key="result.character.id"
              class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
              @click="selectResult(result)"
            >
              <div class="flex items-center space-x-3">
                <img
                  v-if="result.character.avatar_url"
                  :src="result.character.avatar_url"
                  :alt="result.character.name"
                  class="w-16 h-16 rounded-full object-cover"
                />
                <div class="flex-1">
                  <h5 class="font-medium text-gray-900">
                    {{ result.character.name || '未命名角色' }}
                  </h5>
                  <p class="text-sm text-gray-500">
                    相似度: {{ Math.round(result.similarity * 100) }}%
                  </p>
                  <p class="text-sm text-gray-500">
                    时间: {{ formatTimestamp(result.timestamp) }}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 无结果提示 -->
        <div v-else-if="hasSearched && !searching" class="text-center py-8">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">未找到相似角色</h3>
          <p class="mt-1 text-sm text-gray-500">
            尝试调整相似度阈值或使用其他图片
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'
import { useCharactersStore } from '@/stores/characters'

export default {
  name: 'FaceSearchModal',
  props: {
    taskId: {
      type: Number,
      default: null
    }
  },
  emits: ['close', 'search-result'],
  setup(props, { emit }) {
    const charactersStore = useCharactersStore()
    
    // 响应式数据
    const selectedImage = ref(null)
    const selectedFile = ref(null)
    const isDragging = ref(false)
    const searching = ref(false)
    const hasSearched = ref(false)
    const searchResults = ref([])
    
    const searchParams = reactive({
      similarity_threshold: 0.6,
      top_k: 10
    })

    // 方法
    const handleFileSelect = (event) => {
      const file = event.target.files[0]
      if (file) {
        processFile(file)
      }
    }

    const handleDrop = (event) => {
      event.preventDefault()
      isDragging.value = false
      
      const files = event.dataTransfer.files
      if (files.length > 0) {
        processFile(files[0])
      }
    }

    const processFile = (file) => {
      if (!file.type.startsWith('image/')) {
        alert('请选择图片文件')
        return
      }

      selectedFile.value = file
      
      const reader = new FileReader()
      reader.onload = (e) => {
        selectedImage.value = e.target.result
      }
      reader.readAsDataURL(file)
    }

    const clearImage = () => {
      selectedImage.value = null
      selectedFile.value = null
      searchResults.value = []
      hasSearched.value = false
    }

    const performSearch = async () => {
      if (!selectedFile.value) return

      searching.value = true
      hasSearched.value = false

      try {
        // 将文件转换为 base64
        const base64 = await fileToBase64(selectedFile.value)
        
        const searchData = {
          image_base64: base64.split(',')[1], // 移除 data:image/...;base64, 前缀
          task_id: props.taskId,
          similarity_threshold: searchParams.similarity_threshold,
          top_k: searchParams.top_k
        }

        const response = await charactersStore.searchSimilarCharacters(searchData)
        searchResults.value = response.results || []
        hasSearched.value = true
        
        emit('search-result', searchResults.value)
      } catch (error) {
        console.error('搜索失败:', error)
        alert('搜索失败，请重试')
      } finally {
        searching.value = false
      }
    }

    const selectResult = (result) => {
      emit('search-result', [result])
      emit('close')
    }

    const fileToBase64 = (file) => {
      return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.readAsDataURL(file)
        reader.onload = () => resolve(reader.result)
        reader.onerror = error => reject(error)
      })
    }

    const formatTimestamp = (timestamp) => {
      const minutes = Math.floor(timestamp / 60)
      const seconds = Math.floor(timestamp % 60)
      return `${minutes}:${seconds.toString().padStart(2, '0')}`
    }

    return {
      // 数据
      selectedImage,
      selectedFile,
      isDragging,
      searching,
      hasSearched,
      searchResults,
      searchParams,
      
      // 方法
      handleFileSelect,
      handleDrop,
      clearImage,
      performSearch,
      selectResult,
      formatTimestamp
    }
  }
}
</script>

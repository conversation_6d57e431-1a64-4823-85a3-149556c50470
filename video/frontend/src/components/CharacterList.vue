<template>
  <div class="character-list">
    <!-- 头部工具栏 -->
    <div class="flex items-center justify-between mb-6">
      <div class="flex items-center space-x-4">
        <h2 class="text-xl font-semibold text-gray-900">人物角色</h2>
        <span v-if="characters.length > 0" class="text-sm text-gray-500">
          共 {{ pagination.total }} 个角色
        </span>
      </div>
      
      <div class="flex items-center space-x-3">
        <!-- 搜索框 -->
        <div class="relative">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="搜索角色..."
            class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            @input="handleSearch"
          />
          <svg class="absolute left-3 top-2.5 h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </div>

        <!-- 刷新按钮 -->
        <button
          @click="refreshCharacters"
          :disabled="loading"
          class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
          title="刷新"
        >
          <svg class="w-5 h-5" :class="{ 'animate-spin': loading }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
        </button>

        <!-- 人脸搜索按钮 -->
        <button
          @click="showFaceSearch = true"
          class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          人脸搜索
        </button>
      </div>
    </div>

    <!-- 统计信息 -->
    <div v-if="stats" class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      <div class="bg-blue-50 p-4 rounded-lg">
        <div class="text-2xl font-bold text-blue-600">{{ stats.totalCharacters }}</div>
        <div class="text-sm text-blue-600">总角色数</div>
      </div>
      <div class="bg-green-50 p-4 rounded-lg">
        <div class="text-2xl font-bold text-green-600">{{ formatDuration(stats.totalDuration) }}</div>
        <div class="text-sm text-green-600">总出现时长</div>
      </div>
      <div class="bg-yellow-50 p-4 rounded-lg">
        <div class="text-2xl font-bold text-yellow-600">{{ Math.round(stats.avgConfidence * 100) }}%</div>
        <div class="text-sm text-yellow-600">平均置信度</div>
      </div>
      <div class="bg-purple-50 p-4 rounded-lg">
        <div class="text-2xl font-bold text-purple-600">{{ stats.namedCharacters }}</div>
        <div class="text-sm text-purple-600">已命名角色</div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading && characters.length === 0" class="flex items-center justify-center py-12">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      <span class="ml-2 text-gray-600">加载中...</span>
    </div>

    <!-- 空状态 -->
    <div v-else-if="!loading && characters.length === 0" class="text-center py-12">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900">暂无人物角色</h3>
      <p class="mt-1 text-sm text-gray-500">
        {{ taskId ? '该任务还没有识别出人物角色，请先进行视频分析。' : '请选择一个任务查看人物角色。' }}
      </p>
    </div>

    <!-- 角色网格 -->
    <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      <CharacterCard
        v-for="character in characters"
        :key="character.id"
        :character="character"
        :editable="editable"
        @edit="handleEditCharacter"
        @delete="handleDeleteCharacter"
        @view-details="handleViewDetails"
        @view-appearances="handleViewAppearances"
      />
    </div>

    <!-- 分页 -->
    <div v-if="pagination.total > pagination.limit" class="mt-8 flex items-center justify-between">
      <div class="text-sm text-gray-700">
        显示第 {{ pagination.skip + 1 }} - {{ Math.min(pagination.skip + pagination.limit, pagination.total) }} 条，
        共 {{ pagination.total }} 条
      </div>
      <div class="flex space-x-2">
        <button
          @click="previousPage"
          :disabled="pagination.skip === 0"
          class="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          上一页
        </button>
        <button
          @click="nextPage"
          :disabled="pagination.skip + pagination.limit >= pagination.total"
          class="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          下一页
        </button>
      </div>
    </div>

    <!-- 人脸搜索模态框 -->
    <FaceSearchModal
      v-if="showFaceSearch"
      :task-id="taskId"
      @close="showFaceSearch = false"
      @search-result="handleSearchResult"
    />

    <!-- 角色编辑模态框 -->
    <CharacterEditModal
      v-if="showEditModal"
      :character="editingCharacter"
      @close="showEditModal = false"
      @updated="handleCharacterUpdated"
    />

    <!-- 角色详情模态框 -->
    <CharacterDetailModal
      v-if="showDetailModal"
      :character="selectedCharacter"
      @close="showDetailModal = false"
    />

    <!-- 出现记录模态框 -->
    <CharacterAppearancesModal
      v-if="showAppearancesModal"
      :character="selectedCharacter"
      @close="showAppearancesModal = false"
    />
  </div>
</template>

<script>
import { ref, computed, watch, onMounted } from 'vue'
import { useCharactersStore } from '@/stores/characters'
import CharacterCard from './CharacterCard.vue'
import FaceSearchModal from './FaceSearchModal.vue'
import CharacterEditModal from './CharacterEditModal.vue'
import CharacterDetailModal from './CharacterDetailModal.vue'
import CharacterAppearancesModal from './CharacterAppearancesModal.vue'

export default {
  name: 'CharacterList',
  components: {
    CharacterCard,
    FaceSearchModal,
    CharacterEditModal,
    CharacterDetailModal,
    CharacterAppearancesModal
  },
  props: {
    taskId: {
      type: Number,
      default: null
    },
    editable: {
      type: Boolean,
      default: false
    }
  },
  emits: ['character-selected'],
  setup(props, { emit }) {
    const charactersStore = useCharactersStore()
    
    // 响应式数据
    const searchQuery = ref('')
    const showFaceSearch = ref(false)
    const showEditModal = ref(false)
    const showDetailModal = ref(false)
    const showAppearancesModal = ref(false)
    const editingCharacter = ref(null)
    const selectedCharacter = ref(null)

    // 计算属性
    const characters = computed(() => charactersStore.characters)
    const loading = computed(() => charactersStore.loading)
    const pagination = computed(() => charactersStore.pagination)
    const stats = computed(() => {
      if (!props.taskId) return null
      return charactersStore.getCharacterStats(props.taskId)
    })

    // 方法
    const loadCharacters = async () => {
      if (!props.taskId) return
      try {
        await charactersStore.fetchTaskCharacters(props.taskId)
      } catch (error) {
        console.error('加载人物角色失败:', error)
      }
    }

    const refreshCharacters = () => {
      loadCharacters()
    }

    const handleSearch = () => {
      // 实现搜索逻辑
      console.log('搜索:', searchQuery.value)
    }

    const handleEditCharacter = (character) => {
      editingCharacter.value = character
      showEditModal.value = true
    }

    const handleDeleteCharacter = async (character) => {
      if (confirm(`确定要删除角色"${character.name || '未命名角色'}"吗？`)) {
        try {
          await charactersStore.deleteCharacter(character.id)
        } catch (error) {
          console.error('删除角色失败:', error)
        }
      }
    }

    const handleViewDetails = (character) => {
      selectedCharacter.value = character
      showDetailModal.value = true
      emit('character-selected', character)
    }

    const handleViewAppearances = (character) => {
      selectedCharacter.value = character
      showAppearancesModal.value = true
    }

    const handleCharacterUpdated = () => {
      showEditModal.value = false
      editingCharacter.value = null
      loadCharacters()
    }

    const handleSearchResult = (results) => {
      showFaceSearch.value = false
      // 处理搜索结果
      console.log('搜索结果:', results)
    }

    const previousPage = () => {
      if (pagination.value.skip > 0) {
        charactersStore.pagination.skip = Math.max(0, pagination.value.skip - pagination.value.limit)
        loadCharacters()
      }
    }

    const nextPage = () => {
      if (pagination.value.skip + pagination.value.limit < pagination.value.total) {
        charactersStore.pagination.skip += pagination.value.limit
        loadCharacters()
      }
    }

    const formatDuration = (seconds) => {
      if (!seconds || seconds < 0) return '0秒'
      
      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      const secs = Math.floor(seconds % 60)
      
      if (hours > 0) {
        return `${hours}时${minutes}分`
      } else if (minutes > 0) {
        return `${minutes}分${secs}秒`
      } else {
        return `${secs}秒`
      }
    }

    // 监听 taskId 变化
    watch(() => props.taskId, (newTaskId) => {
      if (newTaskId) {
        loadCharacters()
      } else {
        charactersStore.reset()
      }
    }, { immediate: true })

    // 组件挂载时加载数据
    onMounted(() => {
      if (props.taskId) {
        loadCharacters()
      }
    })

    return {
      // 数据
      searchQuery,
      showFaceSearch,
      showEditModal,
      showDetailModal,
      showAppearancesModal,
      editingCharacter,
      selectedCharacter,
      
      // 计算属性
      characters,
      loading,
      pagination,
      stats,
      
      // 方法
      refreshCharacters,
      handleSearch,
      handleEditCharacter,
      handleDeleteCharacter,
      handleViewDetails,
      handleViewAppearances,
      handleCharacterUpdated,
      handleSearchResult,
      previousPage,
      nextPage,
      formatDuration
    }
  }
}
</script>

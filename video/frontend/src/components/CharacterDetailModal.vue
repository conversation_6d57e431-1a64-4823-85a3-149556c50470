<template>
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
      <!-- 头部 -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200">
        <h3 class="text-xl font-semibold text-gray-900">角色详情</h3>
        <button
          @click="$emit('close')"
          class="text-gray-400 hover:text-gray-600 transition-colors"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <!-- 内容 -->
      <div class="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <!-- 左侧：角色基本信息 -->
          <div class="lg:col-span-1">
            <div class="bg-gray-50 rounded-lg p-6">
              <!-- 头像 -->
              <div class="flex justify-center mb-6">
                <div class="relative">
                  <img
                    v-if="character.avatar_url"
                    :src="character.avatar_url"
                    :alt="character.name || '未命名角色'"
                    class="w-32 h-32 rounded-full object-cover border-4 border-white shadow-lg"
                  />
                  <div v-else class="w-32 h-32 rounded-full bg-gray-200 flex items-center justify-center border-4 border-white shadow-lg">
                    <svg class="w-16 h-16 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
                    </svg>
                  </div>
                  
                  <!-- 置信度标签 -->
                  <div class="absolute -bottom-2 left-1/2 transform -translate-x-1/2">
                    <span 
                      class="px-3 py-1 text-sm font-medium rounded-full"
                      :class="getConfidenceClass(character.confidence)"
                    >
                      {{ Math.round(character.confidence * 100) }}%
                    </span>
                  </div>
                </div>
              </div>

              <!-- 基本信息 -->
              <div class="text-center mb-6">
                <h4 class="text-xl font-semibold text-gray-900 mb-2">
                  {{ character.name || '未命名角色' }}
                </h4>
                <p class="text-sm text-gray-500">
                  创建时间: {{ formatDate(character.created_at) }}
                </p>
              </div>

              <!-- 统计信息 -->
              <div class="space-y-4">
                <div class="flex items-center justify-between p-3 bg-white rounded-lg">
                  <div class="flex items-center">
                    <svg class="w-5 h-5 text-blue-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                    <span class="text-sm text-gray-600">出现次数</span>
                  </div>
                  <span class="font-semibold text-gray-900">{{ character.appearance_count }}</span>
                </div>

                <div class="flex items-center justify-between p-3 bg-white rounded-lg">
                  <div class="flex items-center">
                    <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span class="text-sm text-gray-600">总时长</span>
                  </div>
                  <span class="font-semibold text-gray-900">{{ formatDuration(character.total_duration) }}</span>
                </div>

                <div class="flex items-center justify-between p-3 bg-white rounded-lg">
                  <div class="flex items-center">
                    <svg class="w-5 h-5 text-purple-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2" />
                    </svg>
                    <span class="text-sm text-gray-600">视频数量</span>
                  </div>
                  <span class="font-semibold text-gray-900">{{ character.video_count }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧：出现记录 -->
          <div class="lg:col-span-2">
            <div class="bg-white">
              <div class="flex items-center justify-between mb-4">
                <h5 class="text-lg font-semibold text-gray-900">出现记录</h5>
                <button
                  @click="loadAppearances"
                  :disabled="loadingAppearances"
                  class="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors disabled:opacity-50"
                >
                  <span v-if="loadingAppearances">加载中...</span>
                  <span v-else>刷新</span>
                </button>
              </div>

              <!-- 加载状态 -->
              <div v-if="loadingAppearances" class="flex items-center justify-center py-8">
                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                <span class="ml-2 text-gray-600">加载中...</span>
              </div>

              <!-- 出现记录列表 -->
              <div v-else-if="appearances.length > 0" class="space-y-3 max-h-96 overflow-y-auto">
                <div
                  v-for="appearance in appearances"
                  :key="appearance.id"
                  class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                >
                  <div class="flex items-center justify-between mb-2">
                    <h6 class="font-medium text-gray-900">
                      {{ appearance.video_filename || `视频 ${appearance.video_id}` }}
                    </h6>
                    <span 
                      class="px-2 py-1 text-xs font-medium rounded-full"
                      :class="getConfidenceClass(appearance.confidence)"
                    >
                      {{ Math.round(appearance.confidence * 100) }}%
                    </span>
                  </div>
                  
                  <div class="grid grid-cols-2 gap-4 text-sm text-gray-600">
                    <div>
                      <span class="font-medium">开始时间:</span>
                      {{ formatTimestamp(appearance.start_time) }}
                    </div>
                    <div>
                      <span class="font-medium">结束时间:</span>
                      {{ formatTimestamp(appearance.end_time) }}
                    </div>
                    <div>
                      <span class="font-medium">持续时间:</span>
                      {{ formatDuration(appearance.duration) }}
                    </div>
                    <div>
                      <span class="font-medium">记录时间:</span>
                      {{ formatDate(appearance.created_at) }}
                    </div>
                  </div>
                </div>
              </div>

              <!-- 无数据状态 -->
              <div v-else class="text-center py-8">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">暂无出现记录</h3>
                <p class="mt-1 text-sm text-gray-500">该角色还没有出现记录数据</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useCharactersStore } from '@/stores/characters'

export default {
  name: 'CharacterDetailModal',
  props: {
    character: {
      type: Object,
      required: true
    }
  },
  emits: ['close'],
  setup(props) {
    const charactersStore = useCharactersStore()
    
    // 响应式数据
    const appearances = ref([])
    const loadingAppearances = ref(false)

    // 方法
    const loadAppearances = async () => {
      loadingAppearances.value = true
      try {
        const response = await charactersStore.fetchCharacterAppearances(props.character.id)
        appearances.value = response.appearances || []
      } catch (error) {
        console.error('加载出现记录失败:', error)
      } finally {
        loadingAppearances.value = false
      }
    }

    const getConfidenceClass = (confidence) => {
      if (confidence >= 0.8) {
        return 'bg-green-100 text-green-800'
      } else if (confidence >= 0.6) {
        return 'bg-yellow-100 text-yellow-800'
      } else {
        return 'bg-red-100 text-red-800'
      }
    }

    const formatDuration = (seconds) => {
      if (!seconds || seconds < 0) return '0秒'
      
      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      const secs = Math.floor(seconds % 60)
      
      if (hours > 0) {
        return `${hours}时${minutes}分${secs}秒`
      } else if (minutes > 0) {
        return `${minutes}分${secs}秒`
      } else {
        return `${secs}秒`
      }
    }

    const formatTimestamp = (timestamp) => {
      const hours = Math.floor(timestamp / 3600)
      const minutes = Math.floor((timestamp % 3600) / 60)
      const seconds = Math.floor(timestamp % 60)
      
      if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
      } else {
        return `${minutes}:${seconds.toString().padStart(2, '0')}`
      }
    }

    const formatDate = (dateString) => {
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN')
    }

    // 组件挂载时加载数据
    onMounted(() => {
      loadAppearances()
    })

    return {
      // 数据
      appearances,
      loadingAppearances,
      
      // 方法
      loadAppearances,
      getConfidenceClass,
      formatDuration,
      formatTimestamp,
      formatDate
    }
  }
}
</script>

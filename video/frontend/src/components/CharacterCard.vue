<template>
  <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 overflow-hidden">
    <!-- 头像区域 -->
    <div class="relative h-48 bg-gray-100">
      <img
        v-if="character.avatar_url"
        :src="character.avatar_url"
        :alt="character.name || '未命名角色'"
        class="w-full h-full object-cover"
        @error="handleImageError"
      />
      <div v-else class="w-full h-full flex items-center justify-center text-gray-400">
        <svg class="w-16 h-16" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
        </svg>
      </div>
      
      <!-- 置信度标签 -->
      <div class="absolute top-2 right-2">
        <span 
          class="px-2 py-1 text-xs font-medium rounded-full"
          :class="getConfidenceClass(character.confidence)"
        >
          {{ Math.round(character.confidence * 100) }}%
        </span>
      </div>
    </div>

    <!-- 信息区域 -->
    <div class="p-4">
      <!-- 名称 -->
      <div class="flex items-center justify-between mb-2">
        <h3 class="text-lg font-semibold text-gray-900 truncate">
          {{ character.name || '未命名角色' }}
        </h3>
        <button
          v-if="editable"
          @click="$emit('edit', character)"
          class="text-blue-600 hover:text-blue-800 transition-colors"
          title="编辑角色"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
          </svg>
        </button>
      </div>

      <!-- 统计信息 -->
      <div class="space-y-2 text-sm text-gray-600">
        <div class="flex items-center justify-between">
          <span>出现次数:</span>
          <span class="font-medium">{{ character.appearance_count }}</span>
        </div>
        <div class="flex items-center justify-between">
          <span>总时长:</span>
          <span class="font-medium">{{ formatDuration(character.total_duration) }}</span>
        </div>
        <div class="flex items-center justify-between">
          <span>视频数量:</span>
          <span class="font-medium">{{ character.video_count }}</span>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="mt-4 flex space-x-2">
        <button
          @click="$emit('view-details', character)"
          class="flex-1 bg-blue-600 text-white px-3 py-2 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors"
        >
          查看详情
        </button>
        <button
          @click="$emit('view-appearances', character)"
          class="flex-1 bg-gray-600 text-white px-3 py-2 rounded-md text-sm font-medium hover:bg-gray-700 transition-colors"
        >
          出现记录
        </button>
      </div>

      <!-- 删除按钮 -->
      <div v-if="editable" class="mt-2">
        <button
          @click="$emit('delete', character)"
          class="w-full bg-red-600 text-white px-3 py-2 rounded-md text-sm font-medium hover:bg-red-700 transition-colors"
        >
          删除角色
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CharacterCard',
  props: {
    character: {
      type: Object,
      required: true
    },
    editable: {
      type: Boolean,
      default: false
    }
  },
  emits: ['edit', 'delete', 'view-details', 'view-appearances'],
  methods: {
    /**
     * 获取置信度样式类
     */
    getConfidenceClass(confidence) {
      if (confidence >= 0.8) {
        return 'bg-green-100 text-green-800'
      } else if (confidence >= 0.6) {
        return 'bg-yellow-100 text-yellow-800'
      } else {
        return 'bg-red-100 text-red-800'
      }
    },

    /**
     * 格式化时长
     */
    formatDuration(seconds) {
      if (!seconds || seconds < 0) return '0秒'
      
      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      const secs = Math.floor(seconds % 60)
      
      if (hours > 0) {
        return `${hours}时${minutes}分${secs}秒`
      } else if (minutes > 0) {
        return `${minutes}分${secs}秒`
      } else {
        return `${secs}秒`
      }
    },

    /**
     * 处理图片加载错误
     */
    handleImageError(event) {
      event.target.style.display = 'none'
      event.target.parentElement.querySelector('.text-gray-400').style.display = 'flex'
    }
  }
}
</script>

<style scoped>
/* 自定义样式 */
.character-card {
  transition: transform 0.2s ease-in-out;
}

.character-card:hover {
  transform: translateY(-2px);
}
</style>

<template>
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
      <!-- 头部 -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200">
        <div class="flex items-center space-x-3">
          <img
            v-if="character.avatar_url"
            :src="character.avatar_url"
            :alt="character.name"
            class="w-10 h-10 rounded-full object-cover"
          />
          <div>
            <h3 class="text-lg font-semibold text-gray-900">
              {{ character.name || '未命名角色' }} - 出现记录
            </h3>
            <p class="text-sm text-gray-500">
              共 {{ appearances.length }} 条记录
            </p>
          </div>
        </div>
        <button
          @click="$emit('close')"
          class="text-gray-400 hover:text-gray-600 transition-colors"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <!-- 工具栏 -->
      <div class="p-4 border-b border-gray-200 bg-gray-50">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <!-- 视频过滤 -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">
                筛选视频
              </label>
              <select
                v-model="selectedVideoId"
                @change="filterAppearances"
                class="border border-gray-300 rounded-md px-3 py-1 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">全部视频</option>
                <option
                  v-for="video in uniqueVideos"
                  :key="video.id"
                  :value="video.id"
                >
                  {{ video.filename }}
                </option>
              </select>
            </div>

            <!-- 排序 -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">
                排序方式
              </label>
              <select
                v-model="sortBy"
                @change="sortAppearances"
                class="border border-gray-300 rounded-md px-3 py-1 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="start_time">按时间排序</option>
                <option value="duration">按时长排序</option>
                <option value="confidence">按置信度排序</option>
              </select>
            </div>
          </div>

          <div class="flex items-center space-x-2">
            <!-- 刷新按钮 -->
            <button
              @click="loadAppearances"
              :disabled="loading"
              class="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors disabled:opacity-50"
            >
              <span v-if="loading">加载中...</span>
              <span v-else>刷新</span>
            </button>

            <!-- 导出按钮 -->
            <button
              @click="exportAppearances"
              class="px-3 py-1 text-sm bg-green-100 text-green-700 rounded-md hover:bg-green-200 transition-colors"
            >
              导出
            </button>
          </div>
        </div>
      </div>

      <!-- 内容 -->
      <div class="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
        <!-- 加载状态 -->
        <div v-if="loading" class="flex items-center justify-center py-12">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span class="ml-2 text-gray-600">加载中...</span>
        </div>

        <!-- 出现记录列表 -->
        <div v-else-if="filteredAppearances.length > 0" class="space-y-4">
          <!-- 统计信息 -->
          <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div class="bg-blue-50 p-3 rounded-lg">
              <div class="text-lg font-bold text-blue-600">{{ filteredAppearances.length }}</div>
              <div class="text-sm text-blue-600">出现次数</div>
            </div>
            <div class="bg-green-50 p-3 rounded-lg">
              <div class="text-lg font-bold text-green-600">{{ formatDuration(totalDuration) }}</div>
              <div class="text-sm text-green-600">总时长</div>
            </div>
            <div class="bg-yellow-50 p-3 rounded-lg">
              <div class="text-lg font-bold text-yellow-600">{{ Math.round(averageConfidence * 100) }}%</div>
              <div class="text-sm text-yellow-600">平均置信度</div>
            </div>
            <div class="bg-purple-50 p-3 rounded-lg">
              <div class="text-lg font-bold text-purple-600">{{ uniqueVideos.length }}</div>
              <div class="text-sm text-purple-600">涉及视频</div>
            </div>
          </div>

          <!-- 时间线视图 -->
          <div class="space-y-3">
            <div
              v-for="(appearance, index) in filteredAppearances"
              :key="appearance.id"
              class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
            >
              <div class="flex items-start justify-between">
                <div class="flex-1">
                  <!-- 视频信息 -->
                  <div class="flex items-center space-x-2 mb-2">
                    <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                    </svg>
                    <span class="font-medium text-gray-900">
                      {{ appearance.video_filename || `视频 ${appearance.video_id}` }}
                    </span>
                    <span 
                      class="px-2 py-1 text-xs font-medium rounded-full"
                      :class="getConfidenceClass(appearance.confidence)"
                    >
                      {{ Math.round(appearance.confidence * 100) }}%
                    </span>
                  </div>

                  <!-- 时间信息 -->
                  <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600">
                    <div>
                      <span class="font-medium">开始:</span>
                      {{ formatTimestamp(appearance.start_time) }}
                    </div>
                    <div>
                      <span class="font-medium">结束:</span>
                      {{ formatTimestamp(appearance.end_time) }}
                    </div>
                    <div>
                      <span class="font-medium">时长:</span>
                      {{ formatDuration(appearance.duration) }}
                    </div>
                    <div>
                      <span class="font-medium">记录:</span>
                      {{ formatDate(appearance.created_at) }}
                    </div>
                  </div>

                  <!-- 进度条 -->
                  <div class="mt-3">
                    <div class="flex items-center justify-between text-xs text-gray-500 mb-1">
                      <span>{{ formatTimestamp(appearance.start_time) }}</span>
                      <span>{{ formatTimestamp(appearance.end_time) }}</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        class="bg-blue-600 h-2 rounded-full"
                        :style="{ width: getProgressWidth(appearance) }"
                      ></div>
                    </div>
                  </div>
                </div>

                <!-- 操作按钮 -->
                <div class="ml-4 flex flex-col space-y-2">
                  <button
                    @click="jumpToVideo(appearance)"
                    class="px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors"
                  >
                    跳转播放
                  </button>
                  <button
                    @click="viewDetails(appearance)"
                    class="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
                  >
                    查看详情
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 无数据状态 -->
        <div v-else class="text-center py-12">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">暂无出现记录</h3>
          <p class="mt-1 text-sm text-gray-500">
            {{ selectedVideoId ? '该视频中没有找到此角色的出现记录' : '该角色还没有出现记录数据' }}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useCharactersStore } from '@/stores/characters'

export default {
  name: 'CharacterAppearancesModal',
  props: {
    character: {
      type: Object,
      required: true
    }
  },
  emits: ['close'],
  setup(props, { emit }) {
    const charactersStore = useCharactersStore()
    
    // 响应式数据
    const appearances = ref([])
    const loading = ref(false)
    const selectedVideoId = ref('')
    const sortBy = ref('start_time')

    // 计算属性
    const uniqueVideos = computed(() => {
      const videos = []
      const videoMap = new Map()
      
      appearances.value.forEach(appearance => {
        if (!videoMap.has(appearance.video_id)) {
          videoMap.set(appearance.video_id, {
            id: appearance.video_id,
            filename: appearance.video_filename || `视频 ${appearance.video_id}`
          })
          videos.push(videoMap.get(appearance.video_id))
        }
      })
      
      return videos
    })

    const filteredAppearances = computed(() => {
      let filtered = appearances.value
      
      // 按视频过滤
      if (selectedVideoId.value) {
        filtered = filtered.filter(app => app.video_id === parseInt(selectedVideoId.value))
      }
      
      // 排序
      filtered.sort((a, b) => {
        switch (sortBy.value) {
          case 'duration':
            return b.duration - a.duration
          case 'confidence':
            return b.confidence - a.confidence
          case 'start_time':
          default:
            return a.start_time - b.start_time
        }
      })
      
      return filtered
    })

    const totalDuration = computed(() => {
      return filteredAppearances.value.reduce((sum, app) => sum + app.duration, 0)
    })

    const averageConfidence = computed(() => {
      if (filteredAppearances.value.length === 0) return 0
      return filteredAppearances.value.reduce((sum, app) => sum + app.confidence, 0) / filteredAppearances.value.length
    })

    // 方法
    const loadAppearances = async () => {
      loading.value = true
      try {
        const response = await charactersStore.fetchCharacterAppearances(props.character.id)
        appearances.value = response.appearances || []
      } catch (error) {
        console.error('加载出现记录失败:', error)
      } finally {
        loading.value = false
      }
    }

    const filterAppearances = () => {
      // 过滤逻辑已在计算属性中处理
    }

    const sortAppearances = () => {
      // 排序逻辑已在计算属性中处理
    }

    const getConfidenceClass = (confidence) => {
      if (confidence >= 0.8) {
        return 'bg-green-100 text-green-800'
      } else if (confidence >= 0.6) {
        return 'bg-yellow-100 text-yellow-800'
      } else {
        return 'bg-red-100 text-red-800'
      }
    }

    const formatDuration = (seconds) => {
      if (!seconds || seconds < 0) return '0秒'
      
      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      const secs = Math.floor(seconds % 60)
      
      if (hours > 0) {
        return `${hours}时${minutes}分${secs}秒`
      } else if (minutes > 0) {
        return `${minutes}分${secs}秒`
      } else {
        return `${secs}秒`
      }
    }

    const formatTimestamp = (timestamp) => {
      const hours = Math.floor(timestamp / 3600)
      const minutes = Math.floor((timestamp % 3600) / 60)
      const seconds = Math.floor(timestamp % 60)
      
      if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
      } else {
        return `${minutes}:${seconds.toString().padStart(2, '0')}`
      }
    }

    const formatDate = (dateString) => {
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN')
    }

    const getProgressWidth = (appearance) => {
      // 简单的进度条宽度计算，可以根据实际需求调整
      const maxDuration = Math.max(...appearances.value.map(app => app.duration))
      return `${(appearance.duration / maxDuration) * 100}%`
    }

    const jumpToVideo = (appearance) => {
      // 跳转到视频播放页面的逻辑
      console.log('跳转到视频:', appearance)
      // 这里可以触发路由跳转或其他操作
    }

    const viewDetails = (appearance) => {
      // 查看详情的逻辑
      console.log('查看详情:', appearance)
    }

    const exportAppearances = () => {
      // 导出数据的逻辑
      const csvContent = [
        ['视频文件', '开始时间', '结束时间', '持续时间', '置信度', '记录时间'],
        ...filteredAppearances.value.map(app => [
          app.video_filename || `视频 ${app.video_id}`,
          formatTimestamp(app.start_time),
          formatTimestamp(app.end_time),
          formatDuration(app.duration),
          `${Math.round(app.confidence * 100)}%`,
          formatDate(app.created_at)
        ])
      ].map(row => row.join(',')).join('\n')

      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
      const link = document.createElement('a')
      link.href = URL.createObjectURL(blob)
      link.download = `${props.character.name || '角色'}_出现记录.csv`
      link.click()
    }

    // 组件挂载时加载数据
    onMounted(() => {
      loadAppearances()
    })

    return {
      // 数据
      appearances,
      loading,
      selectedVideoId,
      sortBy,
      
      // 计算属性
      uniqueVideos,
      filteredAppearances,
      totalDuration,
      averageConfidence,
      
      // 方法
      loadAppearances,
      filterAppearances,
      sortAppearances,
      getConfidenceClass,
      formatDuration,
      formatTimestamp,
      formatDate,
      getProgressWidth,
      jumpToVideo,
      viewDetails,
      exportAppearances
    }
  }
}
</script>

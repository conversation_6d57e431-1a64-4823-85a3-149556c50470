# 人物角色分析功能使用指南

## 功能概述

人物角色分析功能是视频分析系统的重要组成部分，能够自动识别和管理视频中的人物角色。该功能基于先进的人脸识别技术，提供了完整的人物角色生命周期管理。

## 主要特性

### 🎯 自动人脸识别
- 使用 InsightFace 技术进行高精度人脸检测
- 自动提取人脸特征向量
- 智能评估人脸图像质量

### 🔍 智能聚类分析
- 跨视频人物聚类，自动识别同一人物
- 支持 DBSCAN 和层次聚类算法
- 自动选择最佳代表头像

### ⏱️ 精确时间追踪
- 记录人物在视频中的精确出现时间
- 生成详细的时间线分析
- 计算总出现时长和频次

### 🔎 相似度搜索
- 支持上传图片搜索相似人物
- 基于 Milvus 向量数据库的高效检索
- 可调节相似度阈值

## 使用方法

### 1. 访问人物角色功能

#### 方式一：通过任务详情页面
1. 进入任务管理页面
2. 点击任意任务进入详情页面
3. 切换到"人物角色"标签页

#### 方式二：通过专门的人物角色管理页面
1. 在主导航栏点击"人物角色"
2. 选择要查看的任务
3. 查看该任务的所有人物角色

### 2. 查看人物角色列表

在人物角色页面，您可以看到：

- **角色头像**：系统自动选择的最清晰头像
- **置信度标签**：识别准确度（绿色≥80%，黄色60-80%，红色<60%）
- **统计信息**：
  - 出现次数：该角色在视频中被检测到的次数
  - 总时长：该角色在所有视频中的累计出现时间
  - 视频数量：该角色出现在多少个视频中

### 3. 管理人物角色

#### 编辑角色名称
1. 点击角色卡片上的"编辑"按钮
2. 在弹出的对话框中修改角色名称
3. 点击"保存"确认修改

#### 查看角色详情
1. 点击角色卡片上的"查看详情"按钮
2. 在详情页面查看：
   - 角色基本信息
   - 详细统计数据
   - 创建和更新时间

#### 查看出现记录
1. 点击角色卡片上的"出现记录"按钮
2. 查看该角色在各个视频中的出现时间段
3. 可以按视频筛选和排序
4. 支持导出数据为 CSV 格式

### 4. 人脸相似度搜索

#### 使用搜索功能
1. 点击页面右上角的"人脸搜索"按钮
2. 上传包含人脸的图片（支持拖拽上传）
3. 调整搜索参数：
   - **相似度阈值**：设置匹配的最低相似度（30%-100%）
   - **返回结果数量**：设置最多返回多少个结果（5-50个）
4. 点击"开始搜索"

#### 查看搜索结果
- 系统会显示相似度最高的角色
- 每个结果显示相似度百分比
- 点击结果可以查看该角色的详细信息

### 5. 数据导出

#### 导出角色数据
1. 在角色详情页面点击"导出数据"
2. 系统会下载包含角色信息的 JSON 文件

#### 导出出现记录
1. 在出现记录页面点击"导出"按钮
2. 系统会下载包含时间记录的 CSV 文件

## 界面说明

### 人物角色卡片
- **头像区域**：显示角色的代表头像
- **置信度标签**：右上角显示识别置信度
- **角色名称**：可编辑的角色名称
- **统计信息**：出现次数、总时长、视频数量
- **操作按钮**：查看详情、出现记录、编辑、删除

### 统计面板
- **总角色数**：当前任务识别出的角色总数
- **总出现时长**：所有角色的累计出现时间
- **平均置信度**：所有角色的平均识别准确度
- **已命名角色**：已经手动命名的角色数量

### 筛选和排序
- **搜索框**：按角色名称搜索
- **视频筛选**：在出现记录中按视频筛选
- **排序选项**：按时间、时长、置信度排序

## 最佳实践

### 1. 角色命名建议
- 使用有意义的名称，如"主角小明"、"反派老板"
- 避免使用数字编号，如"角色1"、"角色2"
- 可以包含角色特征，如"红衣女子"、"戴眼镜的男人"

### 2. 搜索优化技巧
- 上传清晰、正面的人脸图片效果最佳
- 适当调整相似度阈值：
  - 高阈值（80%+）：精确匹配，结果较少但准确
  - 中等阈值（60-80%）：平衡准确性和召回率
  - 低阈值（<60%）：宽松匹配，结果较多但可能不准确

### 3. 数据管理建议
- 定期检查和修正角色名称
- 删除明显错误的角色识别结果
- 导出重要数据进行备份

## 技术说明

### 支持的图片格式
- JPEG (.jpg, .jpeg)
- PNG (.png)
- 建议图片大小：不超过 10MB
- 建议分辨率：至少 200x200 像素

### 性能说明
- 人脸检测：每帧处理时间约 0.1-0.5 秒
- 聚类分析：根据人脸数量，通常 1-10 分钟
- 相似度搜索：通常在 1 秒内完成

### 准确性说明
- 人脸检测准确率：>95%（清晰正面人脸）
- 人物识别准确率：80-95%（取决于图像质量）
- 聚类准确率：85-90%（取决于人脸相似度）

## 常见问题

### Q: 为什么有些人物没有被识别出来？
A: 可能的原因：
- 人脸过小或模糊
- 侧脸或遮挡严重
- 光线条件不佳
- 人脸角度过大

### Q: 如何提高识别准确率？
A: 建议：
- 使用高质量的视频源
- 确保充足的光线
- 避免过度的人脸遮挡
- 定期检查和修正结果

### Q: 搜索结果不准确怎么办？
A: 可以尝试：
- 调整相似度阈值
- 使用更清晰的搜索图片
- 尝试不同角度的人脸图片
- 检查图片中是否包含清晰的人脸

### Q: 如何删除错误的角色？
A: 在角色详情页面点击"删除角色"按钮，确认后即可删除。注意：删除操作不可撤销。

## 更新日志

### v1.0.0 (当前版本)
- ✅ 基础人脸检测和识别功能
- ✅ 智能聚类分析
- ✅ 人物出现时间追踪
- ✅ 相似度搜索功能
- ✅ 数据导出功能
- ✅ 完整的管理界面

### 计划中的功能
- 🔄 人物关系网络分析
- 🔄 批量角色操作
- 🔄 高级筛选和统计
- 🔄 API 接口开放

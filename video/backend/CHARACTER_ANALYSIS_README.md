# 视频人物角色分析功能

## 功能概述

本功能为视频分析系统新增了人物角色识别和聚类功能，能够：

1. **人脸检测与特征提取**：使用 InsightFace 技术检测视频帧中的人脸并提取特征向量
2. **跨视频人物聚类**：在任务级别对所有视频的人脸进行聚类，识别不同的人物角色
3. **人物出现记录**：记录每个人物在各个视频中的出现时间段
4. **向量检索**：使用 Milvus 向量数据库支持人脸相似度检索
5. **API接口**：提供完整的 REST API 用于查询和管理人物角色信息

## 技术架构

### 核心组件

1. **InsightFace 服务** (`app/services/insightface_service.py`)
   - 人脸检测和识别
   - 特征向量提取
   - 人脸质量评估

2. **Milvus 服务** (`app/services/milvus_service.py`)
   - 向量存储和检索
   - 相似度搜索
   - 索引管理

3. **聚类服务** (`app/services/character_clustering_service.py`)
   - DBSCAN/层次聚类算法
   - 聚类质量评估
   - 结果优化

4. **任务模块** (`app/tasks/video_tasks_content_roles.py`)
   - 人脸检测任务
   - 人物聚类任务
   - 出现记录生成

### 数据模型

1. **Character（人物角色）**
   - 存储人物基本信息和代表头像
   - 记录置信度和出现统计

2. **CharacterAppearance（人物出现记录）**
   - 记录人物在视频中的出现时间段
   - 包含置信度和代表帧信息

3. **FaceDetection（人脸检测结果）**
   - 存储原始人脸检测数据
   - 包含特征向量和边界框信息

## 安装和配置

### 1. 安装依赖

```bash
pip install insightface pymilvus scikit-learn onnxruntime-gpu
```

### 2. 配置文件

在 `app/core/config.py` 中添加以下配置：

```python
# InsightFace 人脸识别配置
INSIGHTFACE_MODEL: str = "buffalo_l"
INSIGHTFACE_DEVICE: str = "cuda"  # 或 "cpu"
FACE_DETECTION_SIZE: tuple = (640, 640)
MIN_FACE_SIZE: int = 30
FACE_CONFIDENCE_THRESHOLD: float = 0.5

# Milvus 向量数据库配置
ENABLE_MILVUS: bool = True
MILVUS_HOST: str = "localhost"
MILVUS_PORT: int = 19530
MILVUS_FACE_COLLECTION: str = "face_embeddings"

# 人物聚类配置
CLUSTERING_METHOD: str = "dbscan"
FACE_SIMILARITY_THRESHOLD: float = 0.6
CLUSTERING_MIN_SAMPLES: int = 3
CLUSTERING_EPS: float = 0.5
MAX_CHARACTERS_PER_TASK: int = 20
```

### 3. 启动 Milvus

```bash
# 使用 Docker 启动 Milvus
docker run -d --name milvus-standalone \
  -p 19530:19530 \
  -v milvus_data:/var/lib/milvus \
  milvusdb/milvus:latest standalone
```

### 4. 数据库迁移

```bash
# 生成迁移文件
alembic revision --autogenerate -m "Add character analysis models"

# 执行迁移
alembic upgrade head
```

## 使用方法

### 1. 自动分析

人物角色分析已集成到现有的视频处理流程中。当执行任务分析时，系统会自动：

1. 在视频内容分析阶段进行人脸检测
2. 在所有视频分析完成后进行人物聚类
3. 生成人物出现时间记录

### 2. API 接口

#### 获取任务的人物角色列表

```http
GET /api/v1/characters/tasks/{task_id}/characters
```

#### 获取人物角色详情

```http
GET /api/v1/characters/{character_id}
```

#### 更新人物角色名称

```http
PUT /api/v1/characters/{character_id}
Content-Type: application/json

{
  "name": "主角小明"
}
```

#### 获取人物出现记录

```http
GET /api/v1/characters/{character_id}/appearances
```

#### 人脸相似度搜索

```http
POST /api/v1/characters/search
Content-Type: application/json

{
  "image_base64": "base64编码的图片数据",
  "task_id": 1,
  "top_k": 10,
  "similarity_threshold": 0.6
}
```

### 3. 测试功能

运行测试脚本验证功能：

```bash
cd video/backend
python test_character_analysis.py
```

## 工作流程

### 1. 人脸检测阶段

- 遍历视频的所有关键帧
- 使用 InsightFace 检测人脸
- 提取 512 维特征向量
- 保存裁切的人脸图片
- 计算人脸质量分数

### 2. 人物聚类阶段

- 收集任务下所有视频的人脸数据
- 使用 DBSCAN 或层次聚类算法
- 基于特征向量相似度进行聚类
- 为每个聚类选择最佳代表头像
- 创建人物角色记录

### 3. 出现记录生成

- 将同一人物的检测结果按时间排序
- 合并连续的时间段
- 生成人物在视频中的出现记录
- 计算总出现时长和次数

### 4. 向量存储（可选）

- 将人脸特征向量存储到 Milvus
- 建立高效的相似度检索索引
- 支持实时人脸搜索功能

## 性能优化

### 1. 人脸检测优化

- 调整检测图片尺寸 `FACE_DETECTION_SIZE`
- 设置合适的置信度阈值 `FACE_CONFIDENCE_THRESHOLD`
- 使用 GPU 加速 `INSIGHTFACE_DEVICE="cuda"`

### 2. 聚类优化

- 调整 DBSCAN 参数 `CLUSTERING_EPS` 和 `CLUSTERING_MIN_SAMPLES`
- 限制最大人物数量 `MAX_CHARACTERS_PER_TASK`
- 使用合适的相似度阈值 `FACE_SIMILARITY_THRESHOLD`

### 3. 存储优化

- 定期清理临时人脸图片
- 压缩存储的特征向量
- 优化 Milvus 索引配置

## 故障排除

### 1. InsightFace 初始化失败

- 检查 CUDA 环境配置
- 确认模型文件下载完整
- 验证 onnxruntime 版本兼容性

### 2. Milvus 连接失败

- 确认 Milvus 服务正常运行
- 检查网络连接和端口配置
- 查看 Milvus 日志排查问题

### 3. 聚类效果不佳

- 调整聚类参数
- 检查人脸检测质量
- 增加训练数据量

### 4. 内存使用过高

- 减少批处理大小
- 启用特征向量压缩
- 优化图片存储格式

## 扩展功能

### 1. 人工标注

- 支持手动修正人物名称
- 提供人物合并和拆分功能
- 允许手动调整聚类结果

### 2. 高级搜索

- 支持多人脸联合搜索
- 时间范围过滤
- 置信度阈值调整

### 3. 统计分析

- 人物出现频率统计
- 视频覆盖率分析
- 人物关系网络图

## 注意事项

1. **隐私保护**：确保人脸数据的安全存储和访问控制
2. **性能监控**：监控处理时间和资源使用情况
3. **数据备份**：定期备份人物角色数据和向量索引
4. **版本兼容**：注意 InsightFace 和 Milvus 的版本兼容性

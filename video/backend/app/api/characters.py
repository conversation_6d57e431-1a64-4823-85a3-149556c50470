"""
人物角色相关API接口
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import func

from app.core.database import get_db
from app.models.task import Character, CharacterAppearance, FaceDetection, Video, Task
from app.schemas.character import (
    CharacterResponse, CharacterListResponse, CharacterAppearanceResponse,
    CharacterUpdateRequest, CharacterSearchRequest
)
from app.services.milvus_service import milvus_service
from app.services.insightface_service import insightface_service
import numpy as np
import cv2
import base64

router = APIRouter()


@router.get("/tasks/{task_id}/characters", response_model=CharacterListResponse)
async def get_task_characters(
    task_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: Session = Depends(get_db)
):
    """获取任务的所有人物角色"""
    
    # 检查任务是否存在
    task = db.query(Task).filter(Task.id == task_id).first()
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    
    # 查询人物角色
    query = db.query(Character).filter(Character.task_id == task_id)
    total = query.count()
    
    characters = query.offset(skip).limit(limit).all()
    
    # 转换为响应格式
    character_responses = []
    for character in characters:
        # 获取出现统计
        appearances = db.query(CharacterAppearance).filter(
            CharacterAppearance.character_id == character.id
        ).all()
        
        video_count = len(set(app.video_id for app in appearances))
        
        character_responses.append(CharacterResponse(
            id=character.id,
            task_id=character.task_id,
            name=character.name,
            avatar_url=f"/api/characters/{character.id}/avatar" if character.avatar_path else None,
            confidence=character.confidence,
            appearance_count=character.appearance_count,
            total_duration=character.total_duration,
            video_count=video_count,
            created_at=character.created_at,
            updated_at=character.updated_at
        ))
    
    return CharacterListResponse(
        characters=character_responses,
        total=total,
        skip=skip,
        limit=limit
    )


@router.get("/characters/{character_id}", response_model=CharacterResponse)
async def get_character(character_id: int, db: Session = Depends(get_db)):
    """获取单个人物角色详情"""
    
    character = db.query(Character).filter(Character.id == character_id).first()
    if not character:
        raise HTTPException(status_code=404, detail="Character not found")
    
    # 获取出现统计
    appearances = db.query(CharacterAppearance).filter(
        CharacterAppearance.character_id == character_id
    ).all()
    
    video_count = len(set(app.video_id for app in appearances))
    
    return CharacterResponse(
        id=character.id,
        task_id=character.task_id,
        name=character.name,
        avatar_url=f"/api/characters/{character.id}/avatar" if character.avatar_path else None,
        confidence=character.confidence,
        appearance_count=character.appearance_count,
        total_duration=character.total_duration,
        video_count=video_count,
        created_at=character.created_at,
        updated_at=character.updated_at
    )


@router.put("/characters/{character_id}", response_model=CharacterResponse)
async def update_character(
    character_id: int,
    update_data: CharacterUpdateRequest,
    db: Session = Depends(get_db)
):
    """更新人物角色信息"""
    
    character = db.query(Character).filter(Character.id == character_id).first()
    if not character:
        raise HTTPException(status_code=404, detail="Character not found")
    
    # 更新字段
    if update_data.name is not None:
        character.name = update_data.name
    
    db.commit()
    db.refresh(character)
    
    # 获取出现统计
    appearances = db.query(CharacterAppearance).filter(
        CharacterAppearance.character_id == character_id
    ).all()
    
    video_count = len(set(app.video_id for app in appearances))
    
    return CharacterResponse(
        id=character.id,
        task_id=character.task_id,
        name=character.name,
        avatar_url=f"/api/characters/{character.id}/avatar" if character.avatar_path else None,
        confidence=character.confidence,
        appearance_count=character.appearance_count,
        total_duration=character.total_duration,
        video_count=video_count,
        created_at=character.created_at,
        updated_at=character.updated_at
    )


@router.get("/characters/{character_id}/appearances")
async def get_character_appearances(
    character_id: int,
    video_id: Optional[int] = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: Session = Depends(get_db)
):
    """获取人物角色的出现记录"""
    
    character = db.query(Character).filter(Character.id == character_id).first()
    if not character:
        raise HTTPException(status_code=404, detail="Character not found")
    
    # 构建查询
    query = db.query(CharacterAppearance).filter(
        CharacterAppearance.character_id == character_id
    )
    
    if video_id:
        query = query.filter(CharacterAppearance.video_id == video_id)
    
    total = query.count()
    appearances = query.offset(skip).limit(limit).all()
    
    # 转换为响应格式
    appearance_responses = []
    for appearance in appearances:
        video = db.query(Video).filter(Video.id == appearance.video_id).first()
        
        appearance_responses.append(CharacterAppearanceResponse(
            id=appearance.id,
            character_id=appearance.character_id,
            video_id=appearance.video_id,
            video_filename=video.filename if video else None,
            start_time=appearance.start_time,
            end_time=appearance.end_time,
            duration=appearance.duration,
            confidence=appearance.confidence,
            created_at=appearance.created_at
        ))
    
    return {
        "appearances": appearance_responses,
        "total": total,
        "skip": skip,
        "limit": limit
    }


@router.get("/characters/{character_id}/avatar")
async def get_character_avatar(character_id: int, db: Session = Depends(get_db)):
    """获取人物角色头像"""
    
    character = db.query(Character).filter(Character.id == character_id).first()
    if not character:
        raise HTTPException(status_code=404, detail="Character not found")
    
    if not character.avatar_path:
        raise HTTPException(status_code=404, detail="Avatar not found")
    
    try:
        import os
        from fastapi.responses import FileResponse
        
        if os.path.exists(character.avatar_path):
            return FileResponse(character.avatar_path, media_type="image/jpeg")
        else:
            raise HTTPException(status_code=404, detail="Avatar file not found")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to load avatar: {str(e)}")


@router.post("/characters/search")
async def search_similar_characters(
    search_request: CharacterSearchRequest,
    db: Session = Depends(get_db)
):
    """通过人脸图片搜索相似的人物角色"""
    
    try:
        # 解码base64图片
        image_data = base64.b64decode(search_request.image_base64)
        nparr = np.frombuffer(image_data, np.uint8)
        image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        if image is None:
            raise HTTPException(status_code=400, detail="Invalid image data")
        
        # 初始化InsightFace服务
        if not insightface_service.initialized:
            success = await insightface_service.initialize()
            if not success:
                raise HTTPException(status_code=500, detail="Failed to initialize face recognition service")
        
        # 检测人脸并提取特征
        faces = insightface_service.detect_faces(image)
        if not faces:
            raise HTTPException(status_code=400, detail="No face detected in image")
        
        # 使用第一个检测到的人脸
        query_embedding = faces[0]['embedding']
        
        # 在Milvus中搜索相似人脸
        similar_faces = await milvus_service.search_similar_faces(
            query_embedding=query_embedding,
            task_id=search_request.task_id,
            top_k=search_request.top_k,
            threshold=search_request.similarity_threshold
        )
        
        # 获取对应的人物角色信息
        character_results = []
        for face in similar_faces:
            character_id = face.get('character_id')
            if character_id:
                character = db.query(Character).filter(Character.id == character_id).first()
                if character:
                    character_results.append({
                        'character': CharacterResponse(
                            id=character.id,
                            task_id=character.task_id,
                            name=character.name,
                            avatar_url=f"/api/characters/{character.id}/avatar" if character.avatar_path else None,
                            confidence=character.confidence,
                            appearance_count=character.appearance_count,
                            total_duration=character.total_duration,
                            video_count=0,  # 简化处理
                            created_at=character.created_at,
                            updated_at=character.updated_at
                        ),
                        'similarity': face['similarity'],
                        'video_id': face['video_id'],
                        'timestamp': face['timestamp']
                    })
        
        return {
            'results': character_results,
            'total': len(character_results)
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")


@router.delete("/characters/{character_id}")
async def delete_character(character_id: int, db: Session = Depends(get_db)):
    """删除人物角色"""
    
    character = db.query(Character).filter(Character.id == character_id).first()
    if not character:
        raise HTTPException(status_code=404, detail="Character not found")
    
    # 删除相关的人脸检测记录的character_id关联
    db.query(FaceDetection).filter(
        FaceDetection.character_id == character_id
    ).update({'character_id': None})
    
    # 删除人物角色（级联删除会自动删除相关的出现记录）
    db.delete(character)
    db.commit()
    
    return {"message": "Character deleted successfully"}

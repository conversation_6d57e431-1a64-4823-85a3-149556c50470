"""
API v1 路由汇总
"""

from fastapi import APIRouter
from app.api.v1.endpoints import tasks, videos, analysis, clips, files, auth, users, roles, xinference, vision
from app.api import characters

api_router = APIRouter()

# 认证相关路由
api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])
api_router.include_router(users.router, prefix="/users", tags=["users"])
api_router.include_router(roles.router, prefix="/roles", tags=["roles"])

# 业务功能路由
api_router.include_router(tasks.router, prefix="/tasks", tags=["tasks"])
api_router.include_router(videos.router, prefix="/videos", tags=["videos"])
api_router.include_router(analysis.router, prefix="/analysis", tags=["analysis"])
api_router.include_router(clips.router, prefix="/clips", tags=["clips"])
api_router.include_router(files.router, prefix="/files", tags=["files"])
api_router.include_router(characters.router, prefix="/characters", tags=["characters"])

# AI 服务路由
api_router.include_router(xinference.router, prefix="/xinference", tags=["xinference"])
api_router.include_router(vision.router, prefix="/vision", tags=["vision"])

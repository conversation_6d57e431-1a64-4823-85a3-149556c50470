"""
视觉分析API端点
提供统一的视觉模型调用接口
"""

from fastapi import APIRouter, HTTPException, Depends, Form, File, UploadFile
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
import logging
import tempfile
import os
from pathlib import Path

from app.core.database import get_db
from app.services.vision_model_manager import vision_model_manager
from app.models.task import Video, VideoFrame
from app.core.dependencies import require_task_read

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/models")
async def get_available_models():
    """获取可用的视觉模型列表"""
    try:
        await vision_model_manager.initialize()
        models = vision_model_manager.get_available_models()
        models_info = vision_model_manager.get_models_info()
        
        return {
            "status": "success",
            "data": {
                "available_models": models,
                "models_info": models_info,
                "total_count": len(models)
            }
        }
    except Exception as e:
        logger.error(f"Failed to get available models: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get models: {str(e)}")


@router.get("/health")
async def health_check():
    """检查所有视觉模型的健康状态"""
    try:
        health_status = await vision_model_manager.health_check_all()
        return {
            "status": "success",
            "data": health_status
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(e)}")


@router.post("/analyze-image")
async def analyze_single_image(
    image_path: str = Form(...),
    question: str = Form("请详细描述这张图片的内容"),
    model_name: Optional[str] = Form(None),
    db: Session = Depends(get_db),
    current_user = Depends(require_task_read)
):
    """分析单张图片"""
    try:
        # 验证图片路径
        if not os.path.exists(image_path):
            raise HTTPException(status_code=404, detail="Image file not found")
        
        result = await vision_model_manager.analyze_single_image(
            image_path=image_path,
            question=question,
            model_name=model_name
        )
        
        return {
            "status": "success",
            "message": "Image analysis completed",
            "data": result
        }
        
    except Exception as e:
        logger.error(f"Failed to analyze single image: {e}")
        raise HTTPException(status_code=500, detail=f"Image analysis failed: {str(e)}")


@router.post("/analyze-multiple-images")
async def analyze_multiple_images(
    image_paths: List[str] = Form(...),
    question: str = Form("请分析这些图片的内容和关联性"),
    model_name: Optional[str] = Form(None),
    db: Session = Depends(get_db),
    current_user = Depends(require_task_read)
):
    """分析多张图片"""
    try:
        # 验证图片路径
        valid_paths = []
        for path in image_paths:
            if os.path.exists(path):
                valid_paths.append(path)
            else:
                logger.warning(f"Image file not found: {path}")
        
        if not valid_paths:
            raise HTTPException(status_code=404, detail="No valid image files found")
        
        result = await vision_model_manager.analyze_multiple_images(
            image_paths=valid_paths,
            question=question,
            model_name=model_name
        )
        
        return {
            "status": "success",
            "message": "Multiple images analysis completed",
            "data": result
        }
        
    except Exception as e:
        logger.error(f"Failed to analyze multiple images: {e}")
        raise HTTPException(status_code=500, detail=f"Multiple images analysis failed: {str(e)}")


@router.post("/compare-images")
async def compare_images(
    image_paths: List[str] = Form(...),
    question: str = Form("请比较这些图片的异同点"),
    model_name: Optional[str] = Form(None),
    db: Session = Depends(get_db),
    current_user = Depends(require_task_read)
):
    """比较多张图片"""
    try:
        # 验证图片路径
        valid_paths = []
        for path in image_paths:
            if os.path.exists(path):
                valid_paths.append(path)
            else:
                logger.warning(f"Image file not found: {path}")
        
        if len(valid_paths) < 2:
            raise HTTPException(status_code=400, detail="At least 2 valid images are required for comparison")
        
        result = await vision_model_manager.compare_images(
            image_paths=valid_paths,
            question=question,
            model_name=model_name
        )
        
        return {
            "status": "success",
            "message": "Image comparison completed",
            "data": result
        }
        
    except Exception as e:
        logger.error(f"Failed to compare images: {e}")
        raise HTTPException(status_code=500, detail=f"Image comparison failed: {str(e)}")


@router.post("/analyze-video-frames/{video_id}")
async def analyze_video_frames(
    video_id: int,
    question: str = Form("请分析这些视频帧的内容"),
    model_name: Optional[str] = Form(None),
    frame_count: int = Form(5),  # 分析的帧数量
    db: Session = Depends(get_db),
    current_user = Depends(require_task_read)
):
    """分析视频的关键帧"""
    try:
        # 检查视频是否存在
        video = db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise HTTPException(status_code=404, detail="Video not found")
        
        # 获取视频帧
        frames = db.query(VideoFrame).filter(
            VideoFrame.video_id == video_id
        ).order_by(VideoFrame.timestamp).limit(frame_count).all()
        
        if not frames:
            raise HTTPException(status_code=404, detail="No video frames found")
        
        # 收集有效的帧路径
        frame_paths = []
        for frame in frames:
            if frame.file_path and os.path.exists(frame.file_path):
                frame_paths.append(frame.file_path)
        
        if not frame_paths:
            raise HTTPException(status_code=404, detail="No valid frame files found")
        
        # 分析帧
        result = await vision_model_manager.analyze_multiple_images(
            image_paths=frame_paths,
            question=f"这些是视频 {video.filename} 的关键帧。{question}",
            model_name=model_name
        )
        
        # 添加视频信息到结果中
        if result.get("success"):
            result["data"]["video_info"] = {
                "video_id": video_id,
                "filename": video.filename,
                "duration": video.duration,
                "analyzed_frames": len(frame_paths)
            }
        
        return {
            "status": "success",
            "message": "Video frames analysis completed",
            "data": result
        }
        
    except Exception as e:
        logger.error(f"Failed to analyze video frames: {e}")
        raise HTTPException(status_code=500, detail=f"Video frames analysis failed: {str(e)}")


@router.post("/upload-and-analyze")
async def upload_and_analyze_images(
    files: List[UploadFile] = File(...),
    question: str = Form("请分析这些图片的内容"),
    model_name: Optional[str] = Form(None),
    current_user = Depends(require_task_read)
):
    """上传并分析图片"""
    temp_files = []
    try:
        # 保存上传的文件到临时目录
        for file in files:
            if not file.filename.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp', '.gif', '.webp')):
                raise HTTPException(status_code=400, detail=f"Unsupported file format: {file.filename}")
            
            # 创建临时文件
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=Path(file.filename).suffix)
            content = await file.read()
            temp_file.write(content)
            temp_file.close()
            temp_files.append(temp_file.name)
        
        if not temp_files:
            raise HTTPException(status_code=400, detail="No valid image files uploaded")
        
        # 分析图片
        if len(temp_files) == 1:
            result = await vision_model_manager.analyze_single_image(
                image_path=temp_files[0],
                question=question,
                model_name=model_name
            )
        else:
            result = await vision_model_manager.analyze_multiple_images(
                image_paths=temp_files,
                question=question,
                model_name=model_name
            )
        
        return {
            "status": "success",
            "message": "Uploaded images analysis completed",
            "data": result
        }
        
    except Exception as e:
        logger.error(f"Failed to analyze uploaded images: {e}")
        raise HTTPException(status_code=500, detail=f"Uploaded images analysis failed: {str(e)}")
    
    finally:
        # 清理临时文件
        for temp_file in temp_files:
            try:
                os.unlink(temp_file)
            except Exception as e:
                logger.warning(f"Failed to delete temp file {temp_file}: {e}")

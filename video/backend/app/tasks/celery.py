"""
Celery配置和任务定义
"""

from celery import Celery
from app.core.config import settings

# 创建Celery实例
celery = Celery(
    "video_analysis",
    broker=settings.CELERY_BROKER_URL,
    backend=settings.CELERY_RESULT_BACKEND,
    include=[
        'app.tasks.video_tasks',
        'app.tasks.video_tasks_basic_info',
        'app.tasks.video_tasks_content',
        'app.tasks.video_tasks_content_roles_simple',  # 简化版人物角色分析任务
        'app.tasks.video_tasks_plot',
        'app.tasks.video_tasks_minicpm'
    ]
)

# 确保任务被正确导入
celery.autodiscover_tasks(['app.tasks'])

# Celery配置
celery.conf.update(
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
    task_track_started=True,
    task_time_limit=30 * 60,  # 30分钟超时
    task_soft_time_limit=25 * 60,  # 25分钟软超时
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
    broker_connection_retry_on_startup = True
)

# 任务路由配置
celery.conf.task_routes = {
    'app.tasks.video_tasks.*': {'queue': 'video_analysis'},
    'app.tasks.video_tasks_basic_info.*': {'queue': 'video_analysis'},
    'app.tasks.video_tasks_content.*': {'queue': 'video_analysis'},
    'app.tasks.video_tasks_content_roles_simple.*': {'queue': 'video_analysis'},
    'app.tasks.video_tasks_plot.*': {'queue': 'video_analysis'},
    'app.tasks.video_tasks_minicpm.*': {'queue': 'video_analysis'},
}

if __name__ == '__main__':
    celery.start()

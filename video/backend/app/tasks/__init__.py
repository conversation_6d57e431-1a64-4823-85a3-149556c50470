"""
任务模块初始化文件
确保所有任务模块被正确导入，以便 Celery 能够发现和注册任务
"""

# 导入所有任务模块，确保任务被注册
from . import video_tasks
from . import video_tasks_basic_info
from . import video_tasks_content
from . import video_tasks_content_roles_simple  # 简化版人物角色分析任务
from . import video_tasks_plot
from . import video_tasks_minicpm

# 导出所有任务函数，方便其他模块导入
from .video_tasks import process_task_videos
from .video_tasks_basic_info import analyze_video_basic_info
from .video_tasks_content import analyze_video_content
from .video_tasks_content_roles_simple import (
    analyze_video_face_detection,
    analyze_task_character_clustering,
    generate_character_appearances
)
from .video_tasks_plot import analyze_video_plot
from .video_tasks_minicpm import comprehensive_minicpm_analysis

__all__ = [
    # 主要任务
    'process_task_videos',
    
    # 视频分析任务
    'analyze_video_basic_info',
    'analyze_video_content',
    'analyze_video_plot',
    'comprehensive_minicpm_analysis',
    
    # 人物角色分析任务
    'analyze_video_face_detection',
    'analyze_task_character_clustering',
    'generate_character_appearances',
]

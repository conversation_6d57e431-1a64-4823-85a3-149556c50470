"""
视频人物角色分析任务
用于检测、识别和聚类视频中的人物角色
"""

import os
import cv2
import numpy as np
import time
from typing import List, Dict, Any, Optional
from datetime import datetime
from loguru import logger
from sqlalchemy.orm import Session

from celery import current_task
from app.tasks.celery import celery
from app.core.database import SessionLocal
from app.models.task import Video, VideoFrame, FaceDetection, Character, CharacterAppearance
# 延迟导入服务，避免启动时的依赖问题
# from app.services.insightface_service import insightface_service
# from app.services.milvus_service import milvus_service
# from app.services.character_clustering_service import character_clustering_service
from app.core.config import settings
from .task_logger import TaskLogger


@celery.task(bind=True)
def analyze_video_face_detection(self, video_id: int):
    """
    分析单个视频的人脸检测
    提取视频帧中的人脸特征，为后续聚类做准备
    """
    task_logger = TaskLogger("FACE_DETECTION", video_id=video_id)
    task_logger.start_task(description=f"视频 {video_id} 人脸检测分析")
    
    db = SessionLocal()
    start_time = time.time()
    
    try:
        # 步骤1: 获取视频信息
        task_logger.start_step("获取视频信息")
        video = db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise Exception(f"Video {video_id} not found")
        task_logger.complete_step("获取视频信息", f"视频文件: {video.filename}")
        
        # 步骤2: 初始化InsightFace服务
        task_logger.start_step("初始化人脸识别服务")
        if not insightface_service.initialized:
            # 创建同步初始化方法
            try:
                # 直接调用同步初始化
                success = insightface_service.initialize_sync()
                if not success:
                    raise Exception("Failed to initialize InsightFace service")
            except Exception as e:
                task_logger.log_error("InsightFace服务初始化失败", e)
                # 如果InsightFace初始化失败，跳过人脸检测
                task_logger.complete_step("初始化人脸识别服务", f"跳过人脸检测: {str(e)}")
                return {
                    'status': 'skipped',
                    'video_id': video_id,
                    'faces_detected': 0,
                    'message': 'InsightFace service not available'
                }
        task_logger.complete_step("初始化人脸识别服务", "InsightFace服务已就绪")
        
        # 步骤3: 获取视频帧
        task_logger.start_step("获取视频帧")
        frames = db.query(VideoFrame).filter(VideoFrame.video_id == video_id).all()
        if not frames:
            raise Exception(f"No frames found for video {video_id}")
        task_logger.complete_step("获取视频帧", f"找到 {len(frames)} 个视频帧")
        
        # 步骤4: 人脸检测处理
        task_logger.start_step("人脸检测处理")
        face_detections = []
        processed_frames = 0
        total_faces_detected = 0
        
        # 创建人脸图片存储目录
        face_storage_dir = os.path.join(
            getattr(settings, 'VIDEOS_STORAGE_DIR', '../storage/videos'),
            'faces',
            str(video.task_id),
            str(video_id)
        )
        os.makedirs(face_storage_dir, exist_ok=True)
        
        for i, frame in enumerate(frames):
            try:
                # 读取帧图片
                if not os.path.exists(frame.file_path):
                    logger.warning(f"Frame file not found: {frame.file_path}")
                    continue
                
                image = cv2.imread(frame.file_path)
                if image is None:
                    logger.warning(f"Failed to load frame: {frame.file_path}")
                    continue
                
                # 检测人脸
                faces = insightface_service.detect_faces(image)
                
                # 处理检测到的人脸
                for j, face in enumerate(faces):
                    # 保存人脸图片
                    face_filename = f"face_{frame.id}_{j}.jpg"
                    face_path = os.path.join(face_storage_dir, face_filename)
                    
                    # 增强人脸图像质量
                    enhanced_face = insightface_service.enhance_face_image(face['face_image'])
                    
                    # 计算人脸质量分数
                    quality_score = insightface_service.calculate_face_quality(enhanced_face)
                    
                    # 保存人脸图片
                    if insightface_service.save_face_image(enhanced_face, face_path):
                        # 创建人脸检测记录
                        face_detection = FaceDetection(
                            video_id=video_id,
                            frame_id=frame.id,
                            timestamp=frame.timestamp,
                            bbox=face['bbox'],
                            embedding_vector=face['embedding'].tobytes(),
                            confidence=face['confidence'],
                            face_image_path=face_path
                        )
                        
                        # 添加质量分数到face字典，用于后续聚类
                        face['quality_score'] = quality_score
                        face['video_id'] = video_id
                        face['timestamp'] = frame.timestamp
                        face['detection_record'] = face_detection
                        
                        face_detections.append(face_detection)
                        total_faces_detected += 1
                
                processed_frames += 1
                
                # 更新进度
                progress = (processed_frames / len(frames)) * 100
                current_task.update_state(
                    state='PROGRESS',
                    meta={
                        'current': processed_frames,
                        'total': len(frames),
                        'status': f'已处理 {processed_frames}/{len(frames)} 帧，检测到 {total_faces_detected} 个人脸'
                    }
                )
                
                if processed_frames % 10 == 0:
                    task_logger.log_progress(
                        processed_frames, len(frames), 
                        f"人脸检测进度: {total_faces_detected} 个人脸"
                    )
                
            except Exception as e:
                logger.error(f"Failed to process frame {frame.id}: {e}")
                continue
        
        task_logger.complete_step("人脸检测处理", f"处理完成，检测到 {total_faces_detected} 个人脸")
        
        # 步骤5: 保存检测结果
        task_logger.start_step("保存检测结果")
        if face_detections:
            db.add_all(face_detections)
            db.commit()
            task_logger.complete_step("保存检测结果", f"保存了 {len(face_detections)} 条人脸检测记录")
        else:
            task_logger.complete_step("保存检测结果", "未检测到人脸，无需保存")
        
        # 计算处理时间
        processing_time = time.time() - start_time
        
        task_logger.complete_task(
            True, 
            f"人脸检测完成，总耗时: {processing_time:.2f}s，检测到 {total_faces_detected} 个人脸"
        )
        
        return {
            'status': 'completed',
            'video_id': video_id,
            'faces_detected': total_faces_detected,
            'processing_time': processing_time
        }
        
    except Exception as e:
        task_logger.log_error("人脸检测过程中发生异常", e)
        task_logger.complete_task(False, f"人脸检测失败: {str(e)}")
        raise e
    finally:
        db.close()


@celery.task(bind=True)
def analyze_task_character_clustering(self, task_id: int):
    """
    分析任务级别的人物角色聚类
    对任务下所有视频的人脸进行聚类，识别不同的人物角色
    """
    task_logger = TaskLogger("CHARACTER_CLUSTERING", task_id=task_id)
    task_logger.start_task(description=f"任务 {task_id} 人物角色聚类分析")
    
    db = SessionLocal()
    start_time = time.time()
    
    try:
        # 步骤1: 获取任务信息
        task_logger.start_step("获取任务信息")
        from app.models.task import Task
        task = db.query(Task).filter(Task.id == task_id).first()
        if not task:
            raise Exception(f"Task {task_id} not found")
        task_logger.complete_step("获取任务信息", f"任务名称: {task.name}")
        
        # 步骤2: 收集所有人脸检测数据
        task_logger.start_step("收集人脸检测数据")
        face_detections = db.query(FaceDetection).join(Video).filter(
            Video.task_id == task_id
        ).all()
        
        if not face_detections:
            task_logger.complete_step("收集人脸检测数据", "未找到人脸检测数据")
            task_logger.complete_task(True, "无人脸数据，跳过聚类分析")
            return {'status': 'completed', 'message': 'No face data found'}
        
        task_logger.complete_step("收集人脸检测数据", f"收集到 {len(face_detections)} 个人脸数据")
        
        # 步骤3: 准备聚类数据
        task_logger.start_step("准备聚类数据")
        embeddings = []
        face_data = []
        
        for detection in face_detections:
            # 从二进制数据恢复向量
            embedding = np.frombuffer(detection.embedding_vector, dtype=np.float32)
            embeddings.append(embedding)
            
            # 计算人脸质量分数（如果没有保存的话）
            quality_score = 0.8  # 默认质量分数，实际应该从图片计算
            if detection.face_image_path and os.path.exists(detection.face_image_path):
                try:
                    face_image = cv2.imread(detection.face_image_path)
                    if face_image is not None:
                        quality_score = insightface_service.calculate_face_quality(face_image)
                except Exception as e:
                    logger.warning(f"Failed to calculate quality for {detection.face_image_path}: {e}")
            
            face_data.append({
                'detection_id': detection.id,
                'video_id': detection.video_id,
                'timestamp': detection.timestamp,
                'confidence': detection.confidence,
                'quality_score': quality_score,
                'face_image_path': detection.face_image_path,
                'bbox': detection.bbox
            })
        
        task_logger.complete_step("准备聚类数据", f"准备了 {len(embeddings)} 个特征向量")
        
        # 步骤4: 执行聚类分析
        task_logger.start_step("执行聚类分析")
        clustering_result = character_clustering_service.cluster_faces(embeddings, face_data)
        
        clusters = clustering_result['clusters']
        cluster_info = clustering_result['cluster_info']
        noise_points = clustering_result['noise_points']
        
        n_characters = len(set(clusters)) - (1 if -1 in clusters else 0)
        task_logger.complete_step("执行聚类分析", f"识别出 {n_characters} 个人物角色")
        
        # 步骤5: 创建人物角色记录
        task_logger.start_step("创建人物角色记录")
        characters_created = 0
        
        for cluster_id, info in cluster_info.items():
            if cluster_id == -1:  # 跳过噪声点
                continue
            
            # 找到最佳代表人脸
            best_face_idx = info['best_face_index']
            best_detection = face_detections[best_face_idx]
            
            # 创建人物角色记录
            character = Character(
                task_id=task_id,
                name=f"角色_{cluster_id + 1}",  # 默认名称，后续可以手动修改
                avatar_path=best_detection.face_image_path,
                embedding_vector=best_detection.embedding_vector,
                confidence=info['avg_confidence'],
                appearance_count=info['size'],
                total_duration=0.0  # 后续计算
            )
            
            db.add(character)
            db.flush()  # 获取character.id
            
            # 更新相关的人脸检测记录
            for idx in info['indices']:
                face_detections[idx].character_id = character.id
            
            characters_created += 1
        
        # 提交数据库更改
        db.commit()
        task_logger.complete_step("创建人物角色记录", f"创建了 {characters_created} 个人物角色")
        
        # 步骤6: 存储向量到Milvus（可选）
        if getattr(settings, 'ENABLE_MILVUS', False):
            task_logger.start_step("存储向量到Milvus")
            try:
                # 初始化Milvus服务
                if not milvus_service.initialize_sync():
                    raise Exception("Failed to initialize Milvus service")

                # 准备向量数据
                milvus_data = []
                for i, detection in enumerate(face_detections):
                    if detection.character_id:  # 只存储已分配角色的人脸
                        embedding = np.frombuffer(detection.embedding_vector, dtype=np.float32)
                        milvus_data.append({
                            'task_id': task_id,
                            'video_id': detection.video_id,
                            'face_detection_id': detection.id,
                            'timestamp': detection.timestamp,
                            'embedding': embedding.tolist(),
                            'confidence': detection.confidence,
                            'character_id': detection.character_id
                        })

                # 插入向量数据
                if milvus_data:
                    vector_ids = milvus_service.insert_embeddings_sync(milvus_data)
                    task_logger.complete_step("存储向量到Milvus", f"存储了 {len(vector_ids)} 个向量")
                else:
                    task_logger.complete_step("存储向量到Milvus", "无向量数据需要存储")
                    
            except Exception as e:
                logger.warning(f"Failed to store vectors to Milvus: {e}")
                task_logger.complete_step("存储向量到Milvus", f"存储失败: {str(e)}")
        
        # 计算处理时间
        processing_time = time.time() - start_time
        
        task_logger.complete_task(
            True,
            f"人物角色聚类完成，总耗时: {processing_time:.2f}s，识别出 {characters_created} 个角色"
        )
        
        return {
            'status': 'completed',
            'task_id': task_id,
            'characters_created': characters_created,
            'total_faces': len(face_detections),
            'noise_points': len(noise_points),
            'processing_time': processing_time
        }
        
    except Exception as e:
        task_logger.log_error("人物角色聚类过程中发生异常", e)
        task_logger.complete_task(False, f"人物角色聚类失败: {str(e)}")
        raise e
    finally:
        db.close()


@celery.task(bind=True)
def generate_character_appearances(self, task_id: int):
    """
    生成人物出现时间记录
    基于人脸检测结果，生成每个人物在视频中的出现时间段
    """
    task_logger = TaskLogger("CHARACTER_APPEARANCES", task_id=task_id)
    task_logger.start_task(description=f"任务 {task_id} 生成人物出现记录")

    db = SessionLocal()

    try:
        # 获取任务下的所有人物角色
        characters = db.query(Character).filter(Character.task_id == task_id).all()

        if not characters:
            task_logger.complete_task(True, "无人物角色数据")
            return {'status': 'completed', 'message': 'No characters found'}

        appearances_created = 0

        for character in characters:
            # 获取该人物的所有人脸检测记录
            detections = db.query(FaceDetection).filter(
                FaceDetection.character_id == character.id
            ).order_by(FaceDetection.video_id, FaceDetection.timestamp).all()

            if not detections:
                continue

            # 按视频分组处理
            video_detections = {}
            for detection in detections:
                if detection.video_id not in video_detections:
                    video_detections[detection.video_id] = []
                video_detections[detection.video_id].append(detection)

            # 为每个视频生成出现时间段
            for video_id, video_detections_list in video_detections.items():
                appearances = _generate_time_segments(video_detections_list)

                for appearance in appearances:
                    char_appearance = CharacterAppearance(
                        character_id=character.id,
                        video_id=video_id,
                        start_time=appearance['start_time'],
                        end_time=appearance['end_time'],
                        duration=appearance['duration'],
                        frame_path=appearance['representative_frame'],
                        bbox=appearance['representative_bbox'],
                        confidence=appearance['avg_confidence']
                    )
                    db.add(char_appearance)
                    appearances_created += 1

            # 更新人物的总出现时长
            total_duration = sum([
                appearance.duration for appearance in
                db.query(CharacterAppearance).filter(
                    CharacterAppearance.character_id == character.id
                ).all()
            ])
            character.total_duration = total_duration

        db.commit()

        task_logger.complete_task(
            True,
            f"生成人物出现记录完成，创建了 {appearances_created} 条记录"
        )

        return {
            'status': 'completed',
            'task_id': task_id,
            'appearances_created': appearances_created
        }

    except Exception as e:
        task_logger.log_error("生成人物出现记录过程中发生异常", e)
        task_logger.complete_task(False, f"生成失败: {str(e)}")
        raise e
    finally:
        db.close()


def _generate_time_segments(detections: List[FaceDetection], max_gap: float = 5.0) -> List[Dict[str, Any]]:
    """
    将人脸检测记录合并为连续的时间段

    Args:
        detections: 人脸检测记录列表（已按时间排序）
        max_gap: 最大时间间隔（秒），超过此间隔认为是不同的出现段

    Returns:
        时间段列表
    """
    if not detections:
        return []

    segments = []
    current_segment = {
        'start_time': detections[0].timestamp,
        'end_time': detections[0].timestamp,
        'detections': [detections[0]]
    }

    for detection in detections[1:]:
        # 如果时间间隔小于阈值，合并到当前段
        if detection.timestamp - current_segment['end_time'] <= max_gap:
            current_segment['end_time'] = detection.timestamp
            current_segment['detections'].append(detection)
        else:
            # 完成当前段，开始新段
            segments.append(_finalize_segment(current_segment))
            current_segment = {
                'start_time': detection.timestamp,
                'end_time': detection.timestamp,
                'detections': [detection]
            }

    # 添加最后一段
    segments.append(_finalize_segment(current_segment))

    return segments


def _finalize_segment(segment: Dict[str, Any]) -> Dict[str, Any]:
    """完善时间段信息"""
    detections = segment['detections']

    # 找到质量最好的检测作为代表
    best_detection = max(detections, key=lambda d: d.confidence)

    # 计算平均置信度
    avg_confidence = sum(d.confidence for d in detections) / len(detections)

    return {
        'start_time': segment['start_time'],
        'end_time': segment['end_time'],
        'duration': segment['end_time'] - segment['start_time'],
        'representative_frame': best_detection.face_image_path,
        'representative_bbox': best_detection.bbox,
        'avg_confidence': avg_confidence,
        'detection_count': len(detections)
    }

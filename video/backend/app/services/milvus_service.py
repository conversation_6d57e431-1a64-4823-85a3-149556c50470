"""
Milvus向量数据库服务
用于存储和检索人脸特征向量
"""

import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from loguru import logger
from pymilvus import (
    connections, Collection, CollectionSchema, FieldSchema, DataType,
    utility, Index
)
from app.core.config import settings


class MilvusService:
    """Milvus向量数据库服务类"""
    
    def __init__(self):
        self.host = getattr(settings, 'MILVUS_HOST', 'localhost')
        self.port = getattr(settings, 'MILVUS_PORT', 19530)
        self.collection_name = getattr(settings, 'MILVUS_FACE_COLLECTION', 'face_embeddings')
        self.dimension = getattr(settings, 'FACE_EMBEDDING_DIMENSION', 512)  # InsightFace默认512维
        self.connection_alias = "default"
        self.collection = None
        
    async def initialize(self) -> bool:
        """异步初始化Milvus连接和集合"""
        return self.initialize_sync()

    def initialize_sync(self) -> bool:
        """同步初始化Milvus连接和集合"""
        try:
            # 连接到Milvus
            connections.connect(
                alias=self.connection_alias,
                host=self.host,
                port=self.port
            )
            logger.info(f"Connected to Milvus at {self.host}:{self.port}")

            # 创建或获取集合
            self._create_collection_sync()

            # 创建索引
            self._create_index_sync()

            # 加载集合
            self.collection.load()
            logger.info(f"Collection {self.collection_name} loaded successfully")

            return True

        except Exception as e:
            logger.error(f"Failed to initialize Milvus: {e}")
            return False
    
    async def _create_collection(self):
        """异步创建人脸向量集合"""
        self._create_collection_sync()

    def _create_collection_sync(self):
        """同步创建人脸向量集合"""
        if utility.has_collection(self.collection_name):
            self.collection = Collection(self.collection_name)
            logger.info(f"Collection {self.collection_name} already exists")
            return

        # 定义字段
        fields = [
            FieldSchema(name="id", dtype=DataType.INT64, is_primary=True, auto_id=True),
            FieldSchema(name="task_id", dtype=DataType.INT64),
            FieldSchema(name="video_id", dtype=DataType.INT64),
            FieldSchema(name="face_detection_id", dtype=DataType.INT64),
            FieldSchema(name="timestamp", dtype=DataType.FLOAT),
            FieldSchema(name="embedding", dtype=DataType.FLOAT_VECTOR, dim=self.dimension),
            FieldSchema(name="confidence", dtype=DataType.FLOAT),
            FieldSchema(name="character_id", dtype=DataType.INT64, nullable=True)
        ]

        # 创建集合schema
        schema = CollectionSchema(
            fields=fields,
            description="Face embeddings for character recognition"
        )

        # 创建集合
        self.collection = Collection(
            name=self.collection_name,
            schema=schema
        )
        logger.info(f"Collection {self.collection_name} created successfully")
    
    async def _create_index(self):
        """异步创建向量索引"""
        self._create_index_sync()

    def _create_index_sync(self):
        """同步创建向量索引"""
        # 检查是否已有索引
        if self.collection.has_index():
            logger.info("Index already exists")
            return

        # 创建IVF_FLAT索引
        index_params = {
            "metric_type": "L2",  # 欧几里得距离
            "index_type": "IVF_FLAT",
            "params": {"nlist": 128}
        }

        self.collection.create_index(
            field_name="embedding",
            index_params=index_params
        )
        logger.info("Index created successfully")
    
    async def insert_embeddings(
        self,
        embeddings_data: List[Dict[str, Any]]
    ) -> List[int]:
        """异步插入人脸向量数据"""
        return self.insert_embeddings_sync(embeddings_data)

    def insert_embeddings_sync(
        self,
        embeddings_data: List[Dict[str, Any]]
    ) -> List[int]:
        """
        同步插入人脸向量数据

        Args:
            embeddings_data: 向量数据列表，每个元素包含：
                - task_id: 任务ID
                - video_id: 视频ID
                - face_detection_id: 人脸检测记录ID
                - timestamp: 时间戳
                - embedding: 特征向量
                - confidence: 置信度
                - character_id: 人物ID（可选）

        Returns:
            插入的向量ID列表
        """
        try:
            # 准备数据
            data = [
                [item["task_id"] for item in embeddings_data],
                [item["video_id"] for item in embeddings_data],
                [item["face_detection_id"] for item in embeddings_data],
                [item["timestamp"] for item in embeddings_data],
                [item["embedding"] for item in embeddings_data],
                [item["confidence"] for item in embeddings_data],
                [item.get("character_id") for item in embeddings_data]
            ]

            # 插入数据
            result = self.collection.insert(data)

            # 刷新数据
            self.collection.flush()

            logger.info(f"Inserted {len(embeddings_data)} embeddings to Milvus")
            return result.primary_keys

        except Exception as e:
            logger.error(f"Failed to insert embeddings: {e}")
            raise
    
    async def search_similar_faces(
        self,
        query_embedding: np.ndarray,
        task_id: Optional[int] = None,
        top_k: int = 10,
        threshold: float = 0.8
    ) -> List[Dict[str, Any]]:
        """
        搜索相似人脸
        
        Args:
            query_embedding: 查询向量
            task_id: 限制在特定任务内搜索
            top_k: 返回最相似的K个结果
            threshold: 相似度阈值
        
        Returns:
            相似人脸列表
        """
        try:
            # 构建搜索参数
            search_params = {
                "metric_type": "L2",
                "params": {"nprobe": 10}
            }
            
            # 构建过滤表达式
            expr = None
            if task_id is not None:
                expr = f"task_id == {task_id}"
            
            # 执行搜索
            results = self.collection.search(
                data=[query_embedding.tolist()],
                anns_field="embedding",
                param=search_params,
                limit=top_k,
                expr=expr,
                output_fields=["task_id", "video_id", "face_detection_id", 
                              "timestamp", "confidence", "character_id"]
            )
            
            # 处理结果
            similar_faces = []
            for hits in results:
                for hit in hits:
                    # 计算相似度（L2距离转换为相似度）
                    similarity = 1.0 / (1.0 + hit.distance)
                    
                    if similarity >= threshold:
                        similar_faces.append({
                            "id": hit.id,
                            "similarity": similarity,
                            "distance": hit.distance,
                            "task_id": hit.entity.get("task_id"),
                            "video_id": hit.entity.get("video_id"),
                            "face_detection_id": hit.entity.get("face_detection_id"),
                            "timestamp": hit.entity.get("timestamp"),
                            "confidence": hit.entity.get("confidence"),
                            "character_id": hit.entity.get("character_id")
                        })
            
            logger.info(f"Found {len(similar_faces)} similar faces")
            return similar_faces
            
        except Exception as e:
            logger.error(f"Failed to search similar faces: {e}")
            raise
    
    async def update_character_ids(
        self,
        face_detection_ids: List[int],
        character_id: int
    ) -> bool:
        """
        更新人脸检测记录的人物ID
        
        Args:
            face_detection_ids: 人脸检测记录ID列表
            character_id: 人物ID
        
        Returns:
            是否更新成功
        """
        try:
            # 构建更新表达式
            ids_str = ",".join(map(str, face_detection_ids))
            expr = f"face_detection_id in [{ids_str}]"
            
            # 执行更新
            self.collection.delete(expr)
            
            # 重新插入更新后的数据（Milvus不支持直接更新，需要删除后重新插入）
            # 这里需要从数据库重新获取数据并更新character_id
            
            logger.info(f"Updated character_id for {len(face_detection_ids)} face detections")
            return True
            
        except Exception as e:
            logger.error(f"Failed to update character IDs: {e}")
            return False
    
    async def delete_task_data(self, task_id: int) -> bool:
        """
        删除指定任务的所有向量数据
        
        Args:
            task_id: 任务ID
        
        Returns:
            是否删除成功
        """
        try:
            expr = f"task_id == {task_id}"
            self.collection.delete(expr)
            logger.info(f"Deleted all embeddings for task {task_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete task data: {e}")
            return False
    
    async def get_collection_stats(self) -> Dict[str, Any]:
        """获取集合统计信息"""
        try:
            stats = self.collection.get_stats()
            return {
                "row_count": stats["row_count"],
                "collection_name": self.collection_name,
                "dimension": self.dimension
            }
        except Exception as e:
            logger.error(f"Failed to get collection stats: {e}")
            return {}
    
    async def cleanup(self):
        """清理资源"""
        try:
            if self.collection:
                self.collection.release()
            connections.disconnect(self.connection_alias)
            logger.info("Milvus connection closed")
        except Exception as e:
            logger.error(f"Failed to cleanup Milvus: {e}")


# 全局Milvus服务实例
milvus_service = MilvusService()

"""
智谱GLM-4.1V视觉理解模型服务
接入智谱的GLM-4.1V-Thinking模型，支持多图理解能力
文档地址：https://docs.bigmodel.cn/cn/guide/models/vlm/glm-4.1v-thinking
"""

import asyncio
import aiohttp
import base64
import time
from typing import List, Dict, Any, Optional
from pathlib import Path
import logging

from app.services.vision_model_interface import (
    VisionModelInterface, 
    VisionModelError, 
    VisionModelConfig,
    VisionAnalysisResult
)
from app.core.config import settings

logger = logging.getLogger(__name__)


class ZhipuVisionService(VisionModelInterface):
    """智谱GLM-4.1V视觉理解服务"""
    
    def __init__(self):
        config = VisionModelConfig(
            api_key=getattr(settings, 'ZHIPU_API_KEY', ''),
            base_url=getattr(settings, 'ZHIPU_BASE_URL', 'https://open.bigmodel.cn/api/paas/v4'),
            timeout=60,  # 智谱模型可能需要更长时间
            max_retries=3,
            max_images_per_request=8  # GLM-4.1V支持多图，但建议不超过8张
        )
        super().__init__("GLM-4.1V-Thinking", config.to_dict())
        self.config_obj = config
        self.session = None
    
    async def initialize(self) -> bool:
        """初始化服务"""
        try:
            if not self.config_obj.api_key:
                logger.error("Zhipu API key not configured")
                return False
            
            # 创建HTTP会话
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=self.config_obj.timeout),
                headers={
                    "Authorization": f"Bearer {self.config_obj.api_key}",
                    "Content-Type": "application/json"
                }
            )
            
            # 测试连接
            await self._test_connection()
            
            self.is_initialized = True
            logger.info("Zhipu GLM-4.1V vision service initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Zhipu vision service: {e}")
            if self.session:
                await self.session.close()
                self.session = None
            return False
    
    async def _test_connection(self):
        """测试API连接"""
        try:
            # 使用一个简单的文本请求测试连接
            url = f"{self.config_obj.base_url}/chat/completions"
            test_payload = {
                "model": "glm-4-flash",  # 使用轻量级模型测试
                "messages": [{"role": "user", "content": "Hello"}],
                "max_tokens": 10
            }
            
            async with self.session.post(url, json=test_payload) as response:
                if response.status not in [200, 400]:  # 400可能是模型参数问题，但连接正常
                    raise VisionModelError(f"API connection test failed: {response.status}")
        except Exception as e:
            raise VisionModelError(f"Failed to connect to Zhipu API: {e}")
    
    async def analyze_single_image(
        self, 
        image_path: str, 
        question: str = "请详细描述这张图片的内容",
        **kwargs
    ) -> Dict[str, Any]:
        """分析单张图片"""
        if not self.is_initialized:
            raise VisionModelError("Service not initialized", self.model_name)
        
        if not self.validate_image_path(image_path):
            raise VisionModelError(f"Invalid image path: {image_path}", self.model_name)
        
        start_time = time.time()
        
        try:
            # 编码图片
            image_base64 = await self._encode_image(image_path)
            
            # 构建请求
            messages = [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": question
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{image_base64}"
                            }
                        }
                    ]
                }
            ]
            
            # 发送请求
            result = await self._make_request(messages)
            
            processing_time = time.time() - start_time
            
            return VisionAnalysisResult(
                model_name=self.model_name,
                analysis_type="single_image",
                content=result.get("content", ""),
                confidence=result.get("confidence"),
                metadata={
                    "image_path": image_path,
                    "question": question,
                    "thinking_process": result.get("thinking_process"),  # GLM-4.1V-Thinking特有
                    **kwargs
                },
                processing_time=processing_time
            ).to_dict()
            
        except Exception as e:
            logger.error(f"Error analyzing single image: {e}")
            raise VisionModelError(f"Single image analysis failed: {e}", self.model_name)
    
    async def analyze_multiple_images(
        self, 
        image_paths: List[str], 
        question: str = "请分析这些图片的内容和关联性",
        **kwargs
    ) -> Dict[str, Any]:
        """分析多张图片"""
        if not self.is_initialized:
            raise VisionModelError("Service not initialized", self.model_name)
        
        # 验证图片路径
        valid_paths = self.validate_image_paths(image_paths)
        if not valid_paths:
            raise VisionModelError("No valid image paths provided", self.model_name)
        
        # 限制图片数量
        if len(valid_paths) > self.config_obj.max_images_per_request:
            valid_paths = valid_paths[:self.config_obj.max_images_per_request]
            logger.warning(f"Limited to {self.config_obj.max_images_per_request} images")
        
        start_time = time.time()
        
        try:
            # 构建消息内容
            content = [{"type": "text", "text": question}]
            
            # 添加所有图片
            for image_path in valid_paths:
                image_base64 = await self._encode_image(image_path)
                content.append({
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/jpeg;base64,{image_base64}"
                    }
                })
            
            messages = [{"role": "user", "content": content}]
            
            # 发送请求
            result = await self._make_request(messages)
            
            processing_time = time.time() - start_time
            
            return VisionAnalysisResult(
                model_name=self.model_name,
                analysis_type="multiple_images",
                content=result.get("content", ""),
                confidence=result.get("confidence"),
                metadata={
                    "image_paths": valid_paths,
                    "image_count": len(valid_paths),
                    "question": question,
                    "thinking_process": result.get("thinking_process"),
                    **kwargs
                },
                processing_time=processing_time
            ).to_dict()
            
        except Exception as e:
            logger.error(f"Error analyzing multiple images: {e}")
            raise VisionModelError(f"Multiple images analysis failed: {e}", self.model_name)
    
    async def compare_images(
        self, 
        image_paths: List[str], 
        question: str = "请比较这些图片的异同点，分析它们之间的关联性和差异",
        **kwargs
    ) -> Dict[str, Any]:
        """比较多张图片"""
        # GLM-4.1V-Thinking在比较任务上表现优秀，使用专门的提示词
        enhanced_question = f"{question}。请运用你的思维能力，深入分析这些图片的细节差异、相似之处、以及可能的逻辑关联。"
        return await self.analyze_multiple_images(image_paths, enhanced_question, **kwargs)
    
    async def _make_request(self, messages: List[Dict[str, Any]]) -> Dict[str, Any]:
        """发送API请求"""
        url = f"{self.config_obj.base_url}/chat/completions"
        
        payload = {
            "model": "glm-4v-plus",  # 使用GLM-4V Plus模型，支持视觉理解
            "messages": messages,
            "max_tokens": 3000,  # GLM-4.1V-Thinking需要更多token来展示思维过程
            "temperature": 0.7,
            "stream": False
        }
        
        for attempt in range(self.config_obj.max_retries):
            try:
                async with self.session.post(url, json=payload) as response:
                    if response.status == 200:
                        data = await response.json()
                        if "choices" in data and data["choices"]:
                            choice = data["choices"][0]
                            content = choice["message"]["content"]
                            
                            # 解析思维过程（如果有的话）
                            thinking_process = None
                            if "thinking" in choice.get("message", {}):
                                thinking_process = choice["message"]["thinking"]
                            
                            return {
                                "content": content,
                                "thinking_process": thinking_process,
                                "usage": data.get("usage", {})
                            }
                        else:
                            raise VisionModelError("Invalid response format")
                    else:
                        error_text = await response.text()
                        logger.error(f"Zhipu API error: {response.status} - {error_text}")
                        raise VisionModelError(f"API request failed: {response.status} - {error_text}")
                        
            except asyncio.TimeoutError:
                if attempt < self.config_obj.max_retries - 1:
                    await asyncio.sleep(2 ** attempt)  # 指数退避
                    continue
                raise VisionModelError("Request timeout")
            except Exception as e:
                if attempt < self.config_obj.max_retries - 1:
                    await asyncio.sleep(2 ** attempt)
                    continue
                raise
    
    async def _encode_image(self, image_path: str) -> str:
        """将图片编码为base64"""
        try:
            with open(image_path, "rb") as image_file:
                return base64.b64encode(image_file.read()).decode('utf-8')
        except Exception as e:
            raise VisionModelError(f"Failed to encode image {image_path}: {e}")
    
    def get_capabilities(self) -> List[str]:
        """获取模型能力列表"""
        return [
            "single_image_analysis",
            "multiple_images_analysis", 
            "image_comparison",
            "text_recognition",
            "object_detection",
            "scene_understanding",
            "logical_reasoning",  # GLM-4.1V-Thinking特有
            "thinking_process",   # 思维过程展示
            "complex_visual_reasoning"
        ]
    
    async def __aenter__(self):
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()

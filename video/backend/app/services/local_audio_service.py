"""
Local audio processing service for speech recognition
Simplified version with core functionality only
"""

import os
import sys
import traceback
from typing import Any
from loguru import logger
from pydantic import BaseModel, Field

# Import required dependencies
try:
    from modelscope.pipelines import pipeline
    from modelscope.utils.constant import Tasks
    AUDIO_DEPENDENCIES_AVAILABLE = True
except ImportError as e:
    # 打印导入错误详情
    import traceback
    traceback.print_exc()
    logger.warning(f"ModelScope dependencies not available: {e}")
    pipeline = None
    Tasks = None
    AUDIO_DEPENDENCIES_AVAILABLE = False


class AudioRequest(BaseModel):
    """Audio processing request"""
    input: str = Field(..., description="Path to audio file")


class LocalAudioService:
    """Simplified local audio processing service"""

    def __init__(self):
        self.inference_pipeline = None
        self._initialize_pipeline()

    def _initialize_pipeline(self):
        """Initialize the inference pipeline"""
        if not AUDIO_DEPENDENCIES_AVAILABLE:
            logger.error("Audio dependencies not available")
            return

        try:
            # Get GPU ID from environment or default to 0
            gpu_id = os.getenv("GPU_ID", "0")

            logger.info("Initializing inference pipeline...")
            self.inference_pipeline = pipeline(
                task=Tasks.auto_speech_recognition,
                model='iic/speech_paraformer-large-vad-punc_asr_nat-zh-cn-16k-common-vocab8404-pytorch',
                vad_model='iic/speech_fsmn_vad_zh-cn-16k-common-pytorch',
                # punc_model='iic/punc_ct-transformer_zh-cn-common-vocab272727-pytorch',
                punc_model='iic/punc_ct-transformer_cn-en-common-vocab471067-large',
                spk_model="iic/speech_campplus_sv_zh-cn_16k-common",
                lm_model='iic/speech_transformer_lm_zh-cn-common-vocab8404-pytorch',
                device=f"cuda:{gpu_id}",
                batch_size=10,
                batch_size_s=300,
                batch_size_threshold_s=60,
                return_raw_text=True,
                sentence_timestamp=True,
                return_spk_res=True,
                disable_update=True,
            )
            logger.info("Inference pipeline initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize inference pipeline: {e}")
            self.inference_pipeline = None

    def is_available(self) -> bool:
        """Check if audio processing is available"""
        return AUDIO_DEPENDENCIES_AVAILABLE and self.inference_pipeline is not None

    def process_audio(self, request: AudioRequest) -> Any:
        """Process audio file and return recognition result"""
        if not self.is_available():
            raise Exception("Audio processing service is not available")

        try:
            logger.info(f"Processing audio file: {request.input}")
            res = self.inference_pipeline(request.input)
            logger.info("Audio processing completed successfully")
            return res
        except Exception as e:
            logger.error(f"Error processing audio {request.input}: {e}")
            raise Exception(f"Audio processing failed: {str(e)}")


# 全局服务实例（懒加载）
_local_audio_service_instance = None
_service_lock = None


def get_local_audio_service():
    """
    获取本地音频服务实例（懒加载，线程安全）
    只在 Celery worker 中实际使用时才初始化
    """
    global _local_audio_service_instance, _service_lock

    if _local_audio_service_instance is not None:
        return _local_audio_service_instance

    # 导入 threading 模块
    import threading

    # 初始化锁
    if _service_lock is None:
        _service_lock = threading.Lock()

    with _service_lock:
        # 双重检查锁定模式
        if _local_audio_service_instance is not None:
            return _local_audio_service_instance

        try:
            logger.info("正在初始化本地音频服务...")
            _local_audio_service_instance = LocalAudioService()
            logger.info("本地音频服务初始化完成")
            return _local_audio_service_instance
        except Exception as e:
            logger.error(f"本地音频服务初始化失败: {e}")
            raise Exception(f"音频服务初始化失败: {str(e)}")


# 保持向后兼容的别名（已弃用）
def local_audio_service():
    """已弃用：请使用 get_local_audio_service() 函数"""
    logger.warning("local_audio_service 变量已弃用，请使用 get_local_audio_service() 函数")
    return get_local_audio_service()

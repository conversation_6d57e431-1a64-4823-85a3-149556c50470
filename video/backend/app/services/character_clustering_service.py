"""
人物角色聚类服务
用于对人脸特征进行聚类分析，识别不同的人物角色
"""

import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from loguru import logger
from sklearn.cluster import DBSCAN, AgglomerativeClustering
from sklearn.metrics import silhouette_score
from sklearn.preprocessing import StandardScaler
import pickle
import os
from app.core.config import settings


class CharacterClusteringService:
    """人物角色聚类服务类"""
    
    def __init__(self):
        self.clustering_method = getattr(settings, 'CLUSTERING_METHOD', 'dbscan')  # dbscan, hierarchical
        self.similarity_threshold = getattr(settings, 'FACE_SIMILARITY_THRESHOLD', 0.6)
        self.min_samples = getattr(settings, 'CLUSTERING_MIN_SAMPLES', 3)
        self.eps = getattr(settings, 'CLUSTERING_EPS', 0.5)
        self.max_clusters = getattr(settings, 'MAX_CHARACTERS_PER_TASK', 20)
        
    def cluster_faces(
        self, 
        embeddings: List[np.ndarray],
        face_data: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        对人脸特征进行聚类
        
        Args:
            embeddings: 人脸特征向量列表
            face_data: 人脸数据列表，包含检测信息
        
        Returns:
            聚类结果，包含：
            - clusters: 聚类标签
            - cluster_centers: 聚类中心
            - cluster_info: 每个聚类的详细信息
            - noise_points: 噪声点索引
        """
        if len(embeddings) == 0:
            return {
                'clusters': [],
                'cluster_centers': [],
                'cluster_info': {},
                'noise_points': []
            }
        
        try:
            # 转换为numpy数组
            X = np.array(embeddings)
            
            # 标准化特征（可选）
            # scaler = StandardScaler()
            # X_scaled = scaler.fit_transform(X)
            
            # 执行聚类
            if self.clustering_method == 'dbscan':
                clusters = self._dbscan_clustering(X)
            elif self.clustering_method == 'hierarchical':
                clusters = self._hierarchical_clustering(X)
            else:
                raise ValueError(f"Unsupported clustering method: {self.clustering_method}")
            
            # 分析聚类结果
            cluster_info = self._analyze_clusters(clusters, embeddings, face_data)
            
            # 计算聚类中心
            cluster_centers = self._calculate_cluster_centers(clusters, X)
            
            # 识别噪声点
            noise_points = [i for i, label in enumerate(clusters) if label == -1]
            
            logger.info(f"Clustering completed: {len(set(clusters)) - (1 if -1 in clusters else 0)} clusters, {len(noise_points)} noise points")
            
            return {
                'clusters': clusters.tolist(),
                'cluster_centers': cluster_centers,
                'cluster_info': cluster_info,
                'noise_points': noise_points
            }
            
        except Exception as e:
            logger.error(f"Failed to cluster faces: {e}")
            raise
    
    def _dbscan_clustering(self, X: np.ndarray) -> np.ndarray:
        """使用DBSCAN进行聚类"""
        # 计算距离矩阵
        from sklearn.metrics.pairwise import cosine_distances
        distance_matrix = cosine_distances(X)
        
        # 使用预计算的距离矩阵
        dbscan = DBSCAN(
            eps=self.eps,
            min_samples=self.min_samples,
            metric='precomputed'
        )
        
        clusters = dbscan.fit_predict(distance_matrix)
        return clusters
    
    def _hierarchical_clustering(self, X: np.ndarray) -> np.ndarray:
        """使用层次聚类"""
        # 估计聚类数量
        n_clusters = min(self.max_clusters, max(2, len(X) // 10))
        
        clustering = AgglomerativeClustering(
            n_clusters=n_clusters,
            metric='cosine',
            linkage='average'
        )
        
        clusters = clustering.fit_predict(X)
        return clusters
    
    def _analyze_clusters(
        self, 
        clusters: np.ndarray, 
        embeddings: List[np.ndarray],
        face_data: List[Dict[str, Any]]
    ) -> Dict[int, Dict[str, Any]]:
        """分析聚类结果"""
        cluster_info = {}
        unique_clusters = set(clusters)
        
        for cluster_id in unique_clusters:
            if cluster_id == -1:  # 跳过噪声点
                continue
            
            # 获取该聚类的所有点
            cluster_indices = [i for i, c in enumerate(clusters) if c == cluster_id]
            cluster_embeddings = [embeddings[i] for i in cluster_indices]
            cluster_faces = [face_data[i] for i in cluster_indices]
            
            # 计算聚类统计信息
            cluster_info[cluster_id] = {
                'size': len(cluster_indices),
                'indices': cluster_indices,
                'avg_confidence': np.mean([face['confidence'] for face in cluster_faces]),
                'video_distribution': self._get_video_distribution(cluster_faces),
                'time_distribution': self._get_time_distribution(cluster_faces),
                'quality_scores': [face.get('quality_score', 0.0) for face in cluster_faces],
                'best_face_index': self._find_best_face(cluster_faces, cluster_indices)
            }
        
        return cluster_info
    
    def _calculate_cluster_centers(
        self, 
        clusters: np.ndarray, 
        embeddings: np.ndarray
    ) -> Dict[int, np.ndarray]:
        """计算聚类中心"""
        cluster_centers = {}
        unique_clusters = set(clusters)
        
        for cluster_id in unique_clusters:
            if cluster_id == -1:  # 跳过噪声点
                continue
            
            cluster_indices = [i for i, c in enumerate(clusters) if c == cluster_id]
            cluster_embeddings = embeddings[cluster_indices]
            
            # 计算平均向量作为聚类中心
            center = np.mean(cluster_embeddings, axis=0)
            # 归一化
            center = center / np.linalg.norm(center)
            
            cluster_centers[cluster_id] = center
        
        return cluster_centers
    
    def _get_video_distribution(self, cluster_faces: List[Dict[str, Any]]) -> Dict[int, int]:
        """获取聚类在不同视频中的分布"""
        video_count = {}
        for face in cluster_faces:
            video_id = face.get('video_id')
            if video_id:
                video_count[video_id] = video_count.get(video_id, 0) + 1
        return video_count
    
    def _get_time_distribution(self, cluster_faces: List[Dict[str, Any]]) -> Dict[str, float]:
        """获取聚类的时间分布统计"""
        timestamps = [face.get('timestamp', 0.0) for face in cluster_faces]
        if not timestamps:
            return {}
        
        return {
            'min_time': min(timestamps),
            'max_time': max(timestamps),
            'avg_time': np.mean(timestamps),
            'total_duration': max(timestamps) - min(timestamps)
        }
    
    def _find_best_face(
        self, 
        cluster_faces: List[Dict[str, Any]], 
        cluster_indices: List[int]
    ) -> int:
        """找到聚类中质量最好的人脸"""
        best_index = cluster_indices[0]
        best_score = 0.0
        
        for i, face in enumerate(cluster_faces):
            # 综合考虑置信度和质量分数
            confidence = face.get('confidence', 0.0)
            quality = face.get('quality_score', 0.0)
            
            # 综合分数：置信度权重0.6，质量权重0.4
            score = confidence * 0.6 + quality * 0.4
            
            if score > best_score:
                best_score = score
                best_index = cluster_indices[i]
        
        return best_index
    
    def evaluate_clustering(
        self, 
        embeddings: List[np.ndarray], 
        clusters: List[int]
    ) -> Dict[str, float]:
        """评估聚类质量"""
        try:
            X = np.array(embeddings)
            clusters_array = np.array(clusters)
            
            # 过滤噪声点
            valid_indices = clusters_array != -1
            if np.sum(valid_indices) < 2:
                return {'silhouette_score': 0.0, 'n_clusters': 0}
            
            X_valid = X[valid_indices]
            clusters_valid = clusters_array[valid_indices]
            
            # 计算轮廓系数
            if len(set(clusters_valid)) > 1:
                silhouette = silhouette_score(X_valid, clusters_valid, metric='cosine')
            else:
                silhouette = 0.0
            
            return {
                'silhouette_score': float(silhouette),
                'n_clusters': len(set(clusters_valid)),
                'n_noise_points': np.sum(clusters_array == -1),
                'total_points': len(clusters)
            }
            
        except Exception as e:
            logger.error(f"Failed to evaluate clustering: {e}")
            return {'silhouette_score': 0.0, 'n_clusters': 0}
    
    def refine_clusters(
        self,
        embeddings: List[np.ndarray],
        clusters: List[int],
        face_data: List[Dict[str, Any]]
    ) -> List[int]:
        """
        优化聚类结果
        
        Args:
            embeddings: 人脸特征向量
            clusters: 初始聚类结果
            face_data: 人脸数据
        
        Returns:
            优化后的聚类标签
        """
        try:
            refined_clusters = clusters.copy()
            
            # 处理噪声点：尝试将其分配到最近的聚类
            noise_indices = [i for i, c in enumerate(clusters) if c == -1]
            
            for noise_idx in noise_indices:
                noise_embedding = embeddings[noise_idx]
                best_cluster = -1
                best_similarity = 0.0
                
                # 找到最相似的聚类
                for cluster_id in set(clusters):
                    if cluster_id == -1:
                        continue
                    
                    cluster_indices = [i for i, c in enumerate(clusters) if c == cluster_id]
                    cluster_embeddings = [embeddings[i] for i in cluster_indices]
                    
                    # 计算与聚类中心的相似度
                    cluster_center = np.mean(cluster_embeddings, axis=0)
                    cluster_center = cluster_center / np.linalg.norm(cluster_center)
                    
                    similarity = np.dot(noise_embedding, cluster_center)
                    
                    if similarity > best_similarity and similarity > self.similarity_threshold:
                        best_similarity = similarity
                        best_cluster = cluster_id
                
                if best_cluster != -1:
                    refined_clusters[noise_idx] = best_cluster
            
            logger.info(f"Refined clustering: reassigned {len([i for i in noise_indices if refined_clusters[i] != -1])} noise points")
            return refined_clusters
            
        except Exception as e:
            logger.error(f"Failed to refine clusters: {e}")
            return clusters
    
    def save_clustering_model(self, model_data: Dict[str, Any], save_path: str) -> bool:
        """保存聚类模型"""
        try:
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            with open(save_path, 'wb') as f:
                pickle.dump(model_data, f)
            logger.info(f"Clustering model saved to {save_path}")
            return True
        except Exception as e:
            logger.error(f"Failed to save clustering model: {e}")
            return False
    
    def load_clustering_model(self, model_path: str) -> Optional[Dict[str, Any]]:
        """加载聚类模型"""
        try:
            with open(model_path, 'rb') as f:
                model_data = pickle.load(f)
            logger.info(f"Clustering model loaded from {model_path}")
            return model_data
        except Exception as e:
            logger.error(f"Failed to load clustering model: {e}")
            return None


# 全局聚类服务实例
character_clustering_service = CharacterClusteringService()

"""
应用配置管理
"""

from pydantic_settings import BaseSettings
from typing import List
import os


class Settings(BaseSettings):
    """应用配置类"""
    
    # 基础配置
    APP_NAME: str = "短剧视频分析剪辑软件"
    DEBUG: bool = False
    VERSION: str = "1.0.0"
    
    # 服务器配置
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    ALLOWED_HOSTS: List[str] = ["*"]
    
    # 数据库配置
    DATABASE_URL: str = "sqlite:///./app.db"
    
    # Redis配置
    REDIS_URL: str = "redis://localhost:6379/0"
    
    # Celery配置
    CELERY_BROKER_URL: str = "redis://localhost:6379/1"
    CELERY_RESULT_BACKEND: str = "redis://localhost:6379/2"
    
    # JWT配置
    SECRET_KEY: str = "your-secret-key-here"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # 文件存储配置
    UPLOAD_DIR: str = "../storage/uploads"
    VIDEOS_STORAGE_DIR: str = "../storage/videos"
    MAX_FILE_SIZE: int = 1024 * 1024 * 1024 * 2
    
    # 视频处理配置
    VIDEO_FORMATS: List[str] = [".mp4", ".mov", ".avi", ".mkv", ".wmv"]
    
    # 任务配置
    MAX_CONCURRENT_TASKS: int = 3
    TASK_TIMEOUT: int = 3600
    
    # MiniCPM-V-4 配置
    MINICPM_MODEL_NAME: str = "openbmb/MiniCPM-V-4"
    MINICPM_DEVICE: str = "cuda"  # cuda or cpu
    MINICPM_TORCH_DTYPE: str = "bfloat16"  # bfloat16, float16, float32
    MINICPM_ATTENTION_IMPLEMENTATION: str = "sdpa"  # sdpa or flash_attention_2
    MINICPM_MAX_FRAMES_PER_ANALYSIS: int = 10  # 每次分析的最大帧数
    MINICPM_FRAME_SAMPLING_INTERVAL: int = 5  # 帧采样间隔

    # 音频处理配置
    ENABLE_PARSE_AUDIO: bool = False
    AUDIO_BATCH_SIZE: int = 10
    AUDIO_BATCH_SIZE_S: int = 300
    AUDIO_BATCH_SIZE_THRESHOLD_S: int = 60
    AUDIO_MAX_FILE_SIZE: int = 100  # MB
    AUDIO_TEMP_DIR: str = "../storage/tmp/audio_processing"
    AUDIO_CACHE_ENABLED: bool = True
    AUDIO_MAX_CONCURRENT_TASKS: int = 3

    # 字幕处理配置
    SUBTITLE_AUTO_GENERATION_ENABLED: bool = False  # 是否启用字幕自动生成（本地开发环境建议关闭）
    SUBTITLE_LOCAL_SERVICE_ENABLED: bool = False    # 是否启用本地音频服务生成字幕

    # Xinference 配置
    XINFERENCE_ENABLED: bool = False  # 是否启用 Xinference（本地开发环境建议关闭）
    XINFERENCE_BASE_URL: str = "http://**************:9997"  # Xinference 服务地址
    XINFERENCE_API_KEY: str = ""  # API 密钥（可选）
    XINFERENCE_TIMEOUT: int = 30  # 请求超时时间（秒）
    XINFERENCE_MAX_RETRIES: int = 3  # 最大重试次数
    XINFERENCE_CONNECTION_POOL_SIZE: int = 10  # 连接池大小

    # Xinference 模型配置
    XINFERENCE_DEFAULT_LLM: str = "glm4-chat"
    XINFERENCE_DEFAULT_AUDIO: str = "paraformer-zh-spk"
    XINFERENCE_DEFAULT_EMBEDDING: str = "bge-large-zh-v1.5"
    XINFERENCE_DEFAULT_IMAGE: str = "stable-diffusion-xl-base-1.0"
    XINFERENCE_DEFAULT_RERANK: str = "bge-reranker-large"

    # InsightFace 人脸识别配置
    INSIGHTFACE_MODEL: str = "buffalo_l"  # 模型名称
    INSIGHTFACE_DEVICE: str = "cuda"  # cuda 或 cpu
    FACE_DETECTION_SIZE: tuple = (640, 640)  # 检测图像大小
    MIN_FACE_SIZE: int = 30  # 最小人脸尺寸
    FACE_CONFIDENCE_THRESHOLD: float = 0.5  # 人脸检测置信度阈值
    FACE_EMBEDDING_DIMENSION: int = 512  # 人脸特征向量维度

    # Milvus 向量数据库配置
    ENABLE_MILVUS: bool = True  # 是否启用Milvus
    MILVUS_HOST: str = "**************"  # Milvus服务地址
    MILVUS_PORT: int = 19530  # Milvus服务端口
    MILVUS_FACE_COLLECTION: str = "face_embeddings"  # 人脸向量集合名称

    # 人物聚类配置
    CLUSTERING_METHOD: str = "dbscan"  # 聚类方法: dbscan, hierarchical
    FACE_SIMILARITY_THRESHOLD: float = 0.6  # 人脸相似度阈值
    CLUSTERING_MIN_SAMPLES: int = 3  # DBSCAN最小样本数
    CLUSTERING_EPS: float = 0.5  # DBSCAN邻域半径
    MAX_CHARACTERS_PER_TASK: int = 20  # 每个任务最大人物数量

    # Xinference 性能配置
    XINFERENCE_MAX_CONCURRENT_REQUESTS: int = 5  # 最大并发请求数
    XINFERENCE_CACHE_ENABLED: bool = True  # 是否启用结果缓存
    XINFERENCE_CACHE_TTL: int = 3600  # 缓存过期时间（秒）

    # 场景检测配置
    SCENE_DETECTION_THRESHOLD: float = 30.0  # 场景检测阈值
    SCENE_MIN_LENGTH: float = 1.0  # 最小场景长度（秒）
    SCENE_DETECTION_ENABLED: bool = False  # 是否启用场景检测
    SCENE_AUTO_SPLIT_ENABLED: bool = False  # 是否在基础分析中自动分割场景
    SCENE_SPLIT_FORMAT: str = "mp4"  # 场景分割输出格式

    # 视觉模型配置
    # 硅基流动
    SILICONFLOW_API_KEY: str = "sk-bntstadiguzdjgxczscotiatbawbqfgwerywdrznhjtffhix"  # 硅基流动 API 密钥
    SILICONFLOW_BASE_URL: str = "https://api.siliconflow.cn/v1"  # 硅基流动 API 基础URL

    # 智谱 GLM
    ZHIPU_API_KEY: str = "09122890b76f226e087a188ddbc99d54.cSMay5T8ZKR78gBB"  # 智谱 API 密钥
    ZHIPU_BASE_URL: str = "https://open.bigmodel.cn/api/paas/v4"  # 智谱 API 基础URL

    # 火山引擎
    VOLCENGINE_API_KEY: str = "cf674da2-e1f2-4781-ae74-687a8b5a22c4"  # 火山引擎 API 密钥
    VOLCENGINE_BASE_URL: str = "https://ark.cn-beijing.volces.com/api/v3"  # 火山引擎 API 基础URL
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# 创建配置实例
settings = Settings()

# 确保存储目录存在
for directory in [
    settings.UPLOAD_DIR,
    settings.VIDEOS_STORAGE_DIR
]:
    os.makedirs(directory, exist_ok=True)

"""
人物角色相关的Pydantic schemas
"""

from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel, Field


class CharacterBase(BaseModel):
    """人物角色基础模型"""
    name: Optional[str] = Field(None, description="人物名称")


class CharacterCreate(CharacterBase):
    """创建人物角色请求模型"""
    task_id: int = Field(..., description="任务ID")
    avatar_path: Optional[str] = Field(None, description="头像路径")
    confidence: float = Field(0.0, description="识别置信度")


class CharacterUpdate(CharacterBase):
    """更新人物角色请求模型"""
    pass


class CharacterUpdateRequest(BaseModel):
    """更新人物角色请求"""
    name: Optional[str] = Field(None, description="人物名称")


class CharacterResponse(BaseModel):
    """人物角色响应模型"""
    id: int = Field(..., description="人物ID")
    task_id: int = Field(..., description="任务ID")
    name: Optional[str] = Field(None, description="人物名称")
    avatar_url: Optional[str] = Field(None, description="头像URL")
    confidence: float = Field(..., description="识别置信度")
    appearance_count: int = Field(..., description="出现次数")
    total_duration: float = Field(..., description="总出现时长（秒）")
    video_count: int = Field(..., description="出现的视频数量")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True


class CharacterListResponse(BaseModel):
    """人物角色列表响应"""
    characters: List[CharacterResponse] = Field(..., description="人物角色列表")
    total: int = Field(..., description="总数量")
    skip: int = Field(..., description="跳过数量")
    limit: int = Field(..., description="限制数量")


class CharacterAppearanceResponse(BaseModel):
    """人物出现记录响应模型"""
    id: int = Field(..., description="记录ID")
    character_id: int = Field(..., description="人物ID")
    video_id: int = Field(..., description="视频ID")
    video_filename: Optional[str] = Field(None, description="视频文件名")
    start_time: float = Field(..., description="开始时间（秒）")
    end_time: float = Field(..., description="结束时间（秒）")
    duration: float = Field(..., description="持续时间（秒）")
    confidence: float = Field(..., description="检测置信度")
    created_at: datetime = Field(..., description="创建时间")

    class Config:
        from_attributes = True


class CharacterAppearanceListResponse(BaseModel):
    """人物出现记录列表响应"""
    appearances: List[CharacterAppearanceResponse] = Field(..., description="出现记录列表")
    total: int = Field(..., description="总数量")
    skip: int = Field(..., description="跳过数量")
    limit: int = Field(..., description="限制数量")


class FaceDetectionResponse(BaseModel):
    """人脸检测结果响应模型"""
    id: int = Field(..., description="检测记录ID")
    video_id: int = Field(..., description="视频ID")
    timestamp: float = Field(..., description="时间戳（秒）")
    bbox: dict = Field(..., description="边界框坐标")
    confidence: float = Field(..., description="检测置信度")
    face_image_url: Optional[str] = Field(None, description="人脸图片URL")
    character_id: Optional[int] = Field(None, description="关联的人物ID")
    character_name: Optional[str] = Field(None, description="关联的人物名称")
    created_at: datetime = Field(..., description="创建时间")

    class Config:
        from_attributes = True


class CharacterSearchRequest(BaseModel):
    """人物角色搜索请求"""
    image_base64: str = Field(..., description="搜索图片的base64编码")
    task_id: Optional[int] = Field(None, description="限制在特定任务内搜索")
    top_k: int = Field(10, ge=1, le=100, description="返回最相似的K个结果")
    similarity_threshold: float = Field(0.6, ge=0.0, le=1.0, description="相似度阈值")


class CharacterSearchResult(BaseModel):
    """人物角色搜索结果"""
    character: CharacterResponse = Field(..., description="匹配的人物角色")
    similarity: float = Field(..., description="相似度分数")
    video_id: int = Field(..., description="匹配的视频ID")
    timestamp: float = Field(..., description="匹配的时间戳")


class CharacterSearchResponse(BaseModel):
    """人物角色搜索响应"""
    results: List[CharacterSearchResult] = Field(..., description="搜索结果列表")
    total: int = Field(..., description="结果总数")


class CharacterStatistics(BaseModel):
    """人物角色统计信息"""
    total_characters: int = Field(..., description="总人物数量")
    total_appearances: int = Field(..., description="总出现次数")
    total_duration: float = Field(..., description="总出现时长（秒）")
    avg_confidence: float = Field(..., description="平均识别置信度")
    video_coverage: int = Field(..., description="覆盖的视频数量")


class TaskCharacterSummary(BaseModel):
    """任务人物角色摘要"""
    task_id: int = Field(..., description="任务ID")
    task_name: str = Field(..., description="任务名称")
    statistics: CharacterStatistics = Field(..., description="统计信息")
    top_characters: List[CharacterResponse] = Field(..., description="主要人物角色")


class FaceClusteringResult(BaseModel):
    """人脸聚类结果"""
    task_id: int = Field(..., description="任务ID")
    total_faces: int = Field(..., description="总人脸数量")
    characters_created: int = Field(..., description="创建的人物数量")
    noise_points: int = Field(..., description="噪声点数量")
    clustering_quality: float = Field(..., description="聚类质量分数")
    processing_time: float = Field(..., description="处理时间（秒）")


class CharacterMergeRequest(BaseModel):
    """人物角色合并请求"""
    source_character_ids: List[int] = Field(..., description="要合并的源人物ID列表")
    target_character_id: int = Field(..., description="目标人物ID")
    new_name: Optional[str] = Field(None, description="合并后的新名称")


class CharacterSplitRequest(BaseModel):
    """人物角色拆分请求"""
    character_id: int = Field(..., description="要拆分的人物ID")
    face_detection_ids: List[int] = Field(..., description="要拆分出来的人脸检测ID列表")
    new_character_name: Optional[str] = Field(None, description="新人物的名称")

#!/usr/bin/env python3
"""
Celery Worker 启动脚本
确保所有任务模块正确加载
"""

import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def setup_environment():
    """设置环境"""
    print("设置环境变量...")
    
    # 设置必要的环境变量
    os.environ.setdefault('PYTHONPATH', os.path.dirname(os.path.abspath(__file__)))
    
    print("✅ 环境设置完成")

def import_all_tasks():
    """导入所有任务模块"""
    print("导入任务模块...")
    
    try:
        # 按顺序导入所有任务模块
        import app.tasks.video_tasks
        print("✅ video_tasks 导入成功")
        
        import app.tasks.video_tasks_basic_info
        print("✅ video_tasks_basic_info 导入成功")
        
        import app.tasks.video_tasks_content
        print("✅ video_tasks_content 导入成功")
        
        import app.tasks.video_tasks_content_roles
        print("✅ video_tasks_content_roles 导入成功")
        
        import app.tasks.video_tasks_plot
        print("✅ video_tasks_plot 导入成功")
        
        import app.tasks.video_tasks_minicpm
        print("✅ video_tasks_minicpm 导入成功")
        
        # 导入任务包以确保所有任务被注册
        import app.tasks
        print("✅ app.tasks 包导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 任务模块导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_celery_tasks():
    """验证 Celery 任务注册"""
    print("验证 Celery 任务注册...")
    
    try:
        from app.tasks.celery import celery
        
        # 获取所有注册的任务
        registered_tasks = list(celery.tasks.keys())
        
        # 检查人物角色相关任务
        expected_tasks = [
            'app.tasks.video_tasks_content_roles.analyze_video_face_detection',
            'app.tasks.video_tasks_content_roles.analyze_task_character_clustering',
            'app.tasks.video_tasks_content_roles.generate_character_appearances'
        ]
        
        missing_tasks = []
        for task_name in expected_tasks:
            if task_name in registered_tasks:
                print(f"✅ {task_name}")
            else:
                print(f"❌ {task_name} - 未注册")
                missing_tasks.append(task_name)
        
        if missing_tasks:
            print(f"⚠️ 有 {len(missing_tasks)} 个任务未注册")
            return False
        else:
            print("✅ 所有人物角色任务已注册")
            return True
            
    except Exception as e:
        print(f"❌ Celery 任务验证失败: {e}")
        return False

def start_celery_worker():
    """启动 Celery Worker"""
    print("启动 Celery Worker...")
    
    try:
        from app.tasks.celery import celery
        
        # 启动 worker
        celery.worker_main([
            'worker',
            '--loglevel=info',
            '--concurrency=4',
            '--queues=celery,video_analysis',
            '--hostname=worker@%h'
        ])
        
    except KeyboardInterrupt:
        print("\n👋 Celery Worker 已停止")
    except Exception as e:
        print(f"❌ Celery Worker 启动失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🚀 启动 Celery Worker...")
    print("=" * 50)
    
    # 1. 设置环境
    setup_environment()
    print()
    
    # 2. 导入所有任务模块
    if not import_all_tasks():
        print("❌ 任务模块导入失败，退出")
        sys.exit(1)
    print()
    
    # 3. 验证任务注册
    if not verify_celery_tasks():
        print("⚠️ 任务注册验证失败，但继续启动...")
    print()
    
    # 4. 启动 Celery Worker
    start_celery_worker()

if __name__ == "__main__":
    main()

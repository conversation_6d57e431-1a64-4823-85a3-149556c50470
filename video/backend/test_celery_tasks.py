#!/usr/bin/env python3
"""
测试 Celery 任务注册
验证所有任务是否正确注册到 Celery 应用中
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_celery_tasks():
    """测试 Celery 任务注册"""
    print("=== 测试 Celery 任务注册 ===")
    
    try:
        # 导入 Celery 应用
        from app.tasks.celery import celery
        
        print(f"Celery 应用名称: {celery.main}")
        print(f"Broker URL: {celery.conf.broker_url}")
        print(f"Result Backend: {celery.conf.result_backend}")
        
        # 获取所有注册的任务
        registered_tasks = list(celery.tasks.keys())
        print(f"\n注册的任务总数: {len(registered_tasks)}")
        
        # 查找人物角色相关的任务
        character_tasks = [task for task in registered_tasks if 'character' in task or 'face' in task or 'roles' in task]
        
        print("\n=== 人物角色相关任务 ===")
        if character_tasks:
            for task in character_tasks:
                print(f"✅ {task}")
        else:
            print("❌ 未找到人物角色相关任务")
        
        # 检查特定任务
        expected_tasks = [
            'app.tasks.video_tasks_content_roles.analyze_video_face_detection',
            'app.tasks.video_tasks_content_roles.analyze_task_character_clustering',
            'app.tasks.video_tasks_content_roles.generate_character_appearances'
        ]
        
        print("\n=== 检查特定任务 ===")
        for task_name in expected_tasks:
            if task_name in registered_tasks:
                print(f"✅ {task_name}")
            else:
                print(f"❌ {task_name} - 未注册")
        
        # 显示所有任务（可选）
        print("\n=== 所有注册的任务 ===")
        for task in sorted(registered_tasks):
            if not task.startswith('celery.'):  # 过滤掉内置任务
                print(f"  {task}")
        
        return len(character_tasks) > 0
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_task_import():
    """测试任务模块导入"""
    print("\n=== 测试任务模块导入 ===")
    
    try:
        # 测试导入各个任务模块
        modules_to_test = [
            'app.tasks.video_tasks',
            'app.tasks.video_tasks_basic_info',
            'app.tasks.video_tasks_content',
            'app.tasks.video_tasks_content_roles',
            'app.tasks.video_tasks_plot',
            'app.tasks.video_tasks_minicpm'
        ]
        
        for module_name in modules_to_test:
            try:
                __import__(module_name)
                print(f"✅ {module_name}")
            except Exception as e:
                print(f"❌ {module_name}: {e}")
        
        # 测试直接导入任务函数
        print("\n=== 测试任务函数导入 ===")
        try:
            from app.tasks.video_tasks_content_roles import (
                analyze_video_face_detection,
                analyze_task_character_clustering,
                generate_character_appearances
            )
            print("✅ 人物角色任务函数导入成功")
            
            # 检查任务函数是否有正确的装饰器
            print(f"analyze_video_face_detection.name: {analyze_video_face_detection.name}")
            print(f"analyze_task_character_clustering.name: {analyze_task_character_clustering.name}")
            print(f"generate_character_appearances.name: {generate_character_appearances.name}")
            
        except Exception as e:
            print(f"❌ 任务函数导入失败: {e}")
            import traceback
            traceback.print_exc()
        
        return True
        
    except Exception as e:
        print(f"❌ 模块导入测试失败: {e}")
        return False


def test_services():
    """测试服务模块"""
    print("\n=== 测试服务模块 ===")
    
    try:
        # 测试 InsightFace 服务
        try:
            from app.services.insightface_service import insightface_service
            print("✅ InsightFace 服务导入成功")
            print(f"  初始化状态: {insightface_service.initialized}")
        except Exception as e:
            print(f"❌ InsightFace 服务导入失败: {e}")
        
        # 测试 Milvus 服务
        try:
            from app.services.milvus_service import milvus_service
            print("✅ Milvus 服务导入成功")
        except Exception as e:
            print(f"❌ Milvus 服务导入失败: {e}")
        
        # 测试聚类服务
        try:
            from app.services.character_clustering_service import character_clustering_service
            print("✅ 聚类服务导入成功")
        except Exception as e:
            print(f"❌ 聚类服务导入失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 服务测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("开始测试 Celery 任务注册...\n")
    
    results = []
    
    # 运行各项测试
    results.append(test_task_import())
    results.append(test_services())
    results.append(test_celery_tasks())
    
    # 汇总结果
    print("\n=== 测试结果汇总 ===")
    test_names = ["任务模块导入", "服务模块测试", "Celery任务注册"]
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{name}: {status}")
    
    success_count = sum(results)
    total_count = len(results)
    
    print(f"\n总体结果: {success_count}/{total_count} 项测试通过")
    
    if success_count == total_count:
        print("🎉 所有测试通过！")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关配置。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
"""
测试人物角色分析功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import asyncio
from app.services.insightface_service import insightface_service
from app.services.milvus_service import milvus_service
from app.services.character_clustering_service import character_clustering_service
from app.core.database import SessionLocal
from app.models.task import Task, Video, Character
import numpy as np


async def test_insightface_service():
    """测试InsightFace服务"""
    print("=== 测试InsightFace服务 ===")
    
    # 初始化服务
    success = await insightface_service.initialize()
    if success:
        print("✅ InsightFace服务初始化成功")
        
        # 获取模型信息
        info = insightface_service.get_model_info()
        print(f"模型信息: {info}")
    else:
        print("❌ InsightFace服务初始化失败")
    
    return success


async def test_milvus_service():
    """测试Milvus服务"""
    print("\n=== 测试Milvus服务 ===")
    
    try:
        # 初始化服务
        success = await milvus_service.initialize()
        if success:
            print("✅ Milvus服务初始化成功")
            
            # 获取集合统计信息
            stats = await milvus_service.get_collection_stats()
            print(f"集合统计: {stats}")
        else:
            print("❌ Milvus服务初始化失败")
        
        return success
    except Exception as e:
        print(f"❌ Milvus服务测试失败: {e}")
        return False


def test_clustering_service():
    """测试聚类服务"""
    print("\n=== 测试聚类服务 ===")
    
    try:
        # 生成模拟数据
        np.random.seed(42)
        
        # 创建3个聚类的模拟人脸特征
        cluster1 = np.random.normal([0.1, 0.2], 0.05, (10, 512))
        cluster2 = np.random.normal([0.5, 0.6], 0.05, (8, 512))
        cluster3 = np.random.normal([0.8, 0.9], 0.05, (12, 512))
        
        embeddings = np.vstack([cluster1, cluster2, cluster3])
        embeddings = embeddings / np.linalg.norm(embeddings, axis=1, keepdims=True)  # 归一化
        
        # 创建模拟人脸数据
        face_data = []
        for i, embedding in enumerate(embeddings):
            face_data.append({
                'detection_id': i,
                'video_id': 1,
                'timestamp': i * 5.0,
                'confidence': 0.8 + np.random.random() * 0.2,
                'quality_score': 0.7 + np.random.random() * 0.3
            })
        
        # 执行聚类
        result = character_clustering_service.cluster_faces(embeddings.tolist(), face_data)
        
        print(f"✅ 聚类完成")
        print(f"聚类数量: {len(set(result['clusters'])) - (1 if -1 in result['clusters'] else 0)}")
        print(f"噪声点数量: {len(result['noise_points'])}")
        print(f"聚类信息: {list(result['cluster_info'].keys())}")
        
        # 评估聚类质量
        evaluation = character_clustering_service.evaluate_clustering(
            embeddings.tolist(), result['clusters']
        )
        print(f"聚类质量评估: {evaluation}")
        
        return True
        
    except Exception as e:
        print(f"❌ 聚类服务测试失败: {e}")
        return False


def test_database_models():
    """测试数据库模型"""
    print("\n=== 测试数据库模型 ===")
    
    try:
        db = SessionLocal()
        
        # 查询任务数量
        task_count = db.query(Task).count()
        print(f"任务数量: {task_count}")
        
        # 查询视频数量
        video_count = db.query(Video).count()
        print(f"视频数量: {video_count}")
        
        # 查询人物角色数量
        character_count = db.query(Character).count()
        print(f"人物角色数量: {character_count}")
        
        print("✅ 数据库模型测试成功")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库模型测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("开始测试人物角色分析功能...\n")
    
    results = []
    
    # 测试各个组件
    results.append(await test_insightface_service())
    results.append(await test_milvus_service())
    results.append(test_clustering_service())
    results.append(test_database_models())
    
    # 汇总结果
    print("\n=== 测试结果汇总 ===")
    test_names = ["InsightFace服务", "Milvus服务", "聚类服务", "数据库模型"]
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{name}: {status}")
    
    success_count = sum(results)
    total_count = len(results)
    
    print(f"\n总体结果: {success_count}/{total_count} 项测试通过")
    
    if success_count == total_count:
        print("🎉 所有测试通过！人物角色分析功能准备就绪。")
    else:
        print("⚠️ 部分测试失败，请检查相关配置和依赖。")
    
    return success_count == total_count


if __name__ == "__main__":
    # 运行测试
    success = asyncio.run(main())
    sys.exit(0 if success else 1)

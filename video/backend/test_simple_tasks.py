#!/usr/bin/env python3
"""
测试简化版任务注册
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    print("=== 测试简化版任务注册 ===")
    
    try:
        # 1. 测试模块导入
        print("1. 测试模块导入...")
        import app.tasks.video_tasks_content_roles_simple
        print("✅ 简化版任务模块导入成功")
        
        # 2. 测试 Celery 应用
        print("\n2. 测试 Celery 应用...")
        from app.tasks.celery import celery
        print("✅ Celery 应用导入成功")
        
        # 3. 检查任务注册
        print("\n3. 检查任务注册...")
        tasks = list(celery.tasks.keys())
        print(f"总注册任务数: {len(tasks)}")
        
        # 查找简化版任务
        simple_tasks = [t for t in tasks if 'simple' in t]
        print(f"简化版任务数: {len(simple_tasks)}")
        
        for task in simple_tasks:
            print(f"  - {task}")
        
        # 4. 检查特定任务
        print("\n4. 检查特定任务...")
        expected_tasks = [
            'app.tasks.video_tasks_content_roles_simple.analyze_video_face_detection',
            'app.tasks.video_tasks_content_roles_simple.analyze_task_character_clustering',
            'app.tasks.video_tasks_content_roles_simple.generate_character_appearances'
        ]
        
        all_found = True
        for task_name in expected_tasks:
            if task_name in tasks:
                print(f"✅ {task_name}")
            else:
                print(f"❌ {task_name}")
                all_found = False
        
        if all_found:
            print("\n🎉 所有简化版任务都已正确注册！")
            return True
        else:
            print("\n⚠️ 部分任务未注册")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

"""Add short drama metadata fields to tasks table

Revision ID: 0007
Revises: 0006
Create Date: 2025-08-08 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '0007'
down_revision = '0006'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Add short drama metadata fields to tasks table"""
    # Add synopsis field (short drama synopsis/summary)
    op.add_column('tasks', sa.Column('synopsis', sa.Text(), nullable=True))
    
    # Add genre field (short drama genre like romance, suspense, comedy, etc.)
    op.add_column('tasks', sa.Column('genre', sa.String(length=100), nullable=True))
    
    # Add theme field (short drama theme)
    op.add_column('tasks', sa.Column('theme', sa.String(length=200), nullable=True))


def downgrade() -> None:
    """Remove short drama metadata fields from tasks table"""
    # Remove the added columns
    op.drop_column('tasks', 'theme')
    op.drop_column('tasks', 'genre')
    op.drop_column('tasks', 'synopsis')

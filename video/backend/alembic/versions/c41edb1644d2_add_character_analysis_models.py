"""Add character analysis models

Revision ID: c41edb1644d2
Revises: 0007
Create Date: 2025-08-09 21:39:50.328240

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'c41edb1644d2'
down_revision = '0007'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('tasks', 'genre',
               existing_type=sa.VARCHAR(length=100),
               type_=sa.JSON(),
               existing_nullable=True)
    op.alter_column('tasks', 'theme',
               existing_type=sa.VARCHAR(length=200),
               type_=sa.JSON(),
               existing_nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('tasks', 'theme',
               existing_type=sa.JSON(),
               type_=sa.VARCHAR(length=200),
               existing_nullable=True)
    op.alter_column('tasks', 'genre',
               existing_type=sa.JSON(),
               type_=sa.VARCHAR(length=100),
               existing_nullable=True)
    # ### end Alembic commands ###
